import React from 'react';
import { Container, Typography, <PERSON>, Al<PERSON>, Button } from '@mui/material';
import { EmailTestInterface } from '../components/EmailTestInterface';
import { useNavigate } from 'react-router-dom';

const EmailTest: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          🧪 Email System Testing
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Test and verify the email verification system
        </Typography>
      </Box>

      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Before Testing:</strong> Make sure you have:
        </Typography>
        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
          <li>Set up your Resend API key in Supabase Dashboard</li>
          <li>Verified your domain in Resend (<NAME_EMAIL> for testing)</li>
          <li>Deployed the Edge Function</li>
          <li>Applied database migrations</li>
        </ul>
      </Alert>

      <EmailTestInterface />

      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Button 
          variant="outlined" 
          onClick={() => navigate('/')}
          size="large"
        >
          ← Back to Home
        </Button>
      </Box>

      <Alert severity="info" sx={{ mt: 4 }}>
        <Typography variant="body2">
          <strong>Troubleshooting:</strong>
        </Typography>
        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
          <li><strong>403 Error:</strong> Domain not verified in Resend. Use <EMAIL> temporarily.</li>
          <li><strong>500 Error:</strong> Check Edge Function logs and environment variables.</li>
          <li><strong>Database Error:</strong> Ensure migrations are applied and functions exist.</li>
          <li><strong>Network Error:</strong> Check if Edge Function is deployed an