import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase, sessionManager } from '../utils/supabaseClient';
import { useLocation, useNavigate } from 'react-router-dom';
import { SubscriptionData, SubscriptionOverride, GuestUser, GuestUserStatus, GuestUserCreateRequest, GuestUserVerifyRequest, GuestUserLoginRequest, GuestUserResponse, GuestAnalyticsSession } from '../types';
// TEMPORARILY DISABLED: Firefox fixes are causing authentication issues
// import { firefoxAuthHandler } from '../utils/firefoxAuthHandler';
// import { signalAuthenticationStart, signalAuthenticationSuccess } from '../utils/earlyFirefoxInit';
// TEMPORARILY DISABLED: Firefox fixes are causing authentication issues
// import { firefoxAuthRecovery } from '../utils/firefoxAuthRecovery';
// import { productionFirefoxAuthFix } from '../utils/productionFirefoxAuthFix';



interface UserProfile {
  username?: string;
  avatar_url?: string;
  full_name?: string;
  institution?: string;
  country?: string;
  accounttype?: 'standard' | 'pro' | 'edu' | 'edu_pro';
  edu_subscription_type?: 'free' | 'pro' | null;
  is_admin?: boolean;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  userProfile: UserProfile | null;
  isGuest: boolean;
  isAuthenticated: boolean;
  loading: boolean;
  canAccessSampleData: boolean;
  canImportData: boolean;
  canAccessProFeatures: boolean; // New: Access to Advanced Analysis and Publication Ready
  showSignupSuccessMessage: boolean; // New: Flag to show signup success message
  clearSignupSuccessMessage: () => void; // New: Function to clear the message flag
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signUp: (email: string, password: string, options?: { data?: Record<string, any> }) => Promise<{ error: any | null, user: User | null }>;
  signInWithGoogle: () => Promise<{ error: any | null }>; // Add Google sign-in function
  signInWithMagicLink: (email: string) => Promise<{ error: any | null }>; // Add magic link sign-in function
  loginAsGuest: () => void;
  loginAsGuestDirect: () => void; // Simplified guest access without email verification
  logoutGuest: () => void;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any | null }>;
  updateProfile: (data: { username?: string, avatar_url?: string, full_name?: string, institution?: string, country?: string }) => Promise<{ error: any | null }>;
  uploadAvatar: (file: File) => Promise<{ error: any | null, publicUrl?: string | null }>;
  logLoginEvent: (event_type: 'app_open' | 'login' | 'signin' | 'sign_in' | 'signed_in', details?: Record<string, any>) => Promise<void>; // Login event logging only
  accountType: 'standard' | 'pro' | 'edu' | 'edu_pro' | null;
  refreshProfile: () => Promise<void>; // New: Function to refresh profile data
  // New subscription-related properties
  subscriptionData: SubscriptionData | null;
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'incomplete' | 'unpaid' | null;
  hasActiveSubscription: boolean;
  nextPaymentDate: string | null;
  billingCycle: 'monthly' | 'yearly' | null;
  refreshSubscription: () => Promise<void>;
  canUpgradeAccount: boolean;
  // New educational tier properties
  canAccessAdvancedAnalysis: boolean;
  canAccessPublicationReady: boolean;
  canAccessCloudStorage: boolean;
  isEducationalUser: boolean;
  educationalTier: 'free' | 'pro' | null;
  // Admin-related properties
  isAdmin: boolean;
  canAccessAdminDashboard: boolean;
  refreshAdminStatus: () => Promise<void>;
  // Subscription override properties
  subscriptionOverride: SubscriptionOverride | null;
  effectiveTier: 'guest' | 'standard' | 'edu' | 'edu_pro' | 'pro';
  hasActiveOverride: boolean;
  refreshOverrideStatus: () => Promise<void>;
  forceRefreshUser: (userId: string) => Promise<{ error: any | null }>;
  // Guest user management properties
  guestUser: GuestUser | null;
  guestAnalyticsSession: GuestAnalyticsSession | null;
  isEmailVerifiedGuest: boolean;
  createGuestUser: (request: GuestUserCreateRequest) => Promise<GuestUserResponse>;
  verifyGuestEmail: (request: GuestUserVerifyRequest) => Promise<GuestUserResponse>;
  loginAsEmailVerifiedGuest: (request: GuestUserLoginRequest) => Promise<GuestUserResponse>;
  startGuestAnalyticsSession: () => Promise<void>;
  endGuestAnalyticsSession: () => Promise<void>;
  logoutEmailVerifiedGuest: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isGuest, setIsGuest] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [showSignupSuccessMessage, setShowSignupSuccessMessage] = useState(false);
  const [accountType, setAccountType] = useState<'standard' | 'pro' | 'edu' | 'edu_pro' | null>(null);

  // Admin-related state
  const [isAdmin, setIsAdmin] = useState<boolean>(false);

  // Logout state management to prevent race conditions
  const [isLoggingOut, setIsLoggingOut] = useState<boolean>(false);

  // New subscription-related state
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<'active' | 'canceled' | 'past_due' | 'incomplete' | 'unpaid' | null>(null);

  // New educational tier state
  const [educationalTier, setEducationalTier] = useState<'free' | 'pro' | null>(null);

  // Subscription override state
  const [subscriptionOverride, setSubscriptionOverride] = useState<SubscriptionOverride | null>(null);

  // Guest user management state
  const [guestUser, setGuestUser] = useState<GuestUser | null>(null);
  const [guestAnalyticsSession, setGuestAnalyticsSession] = useState<GuestAnalyticsSession | null>(null);

  const location = useLocation();
  const navigate = useNavigate();

  const canAccessSampleData = !!user || isGuest;
  const canImportData = !!user;

  // Guest user computed properties
  const isEmailVerifiedGuest = useMemo(() => {
    return guestUser !== null && guestUser.status === 'verified';
  }, [guestUser]);

  // New educational tier computed properties
  const isEducationalUser = useMemo(() => {
    return accountType === 'edu' || accountType === 'edu_pro';
  }, [accountType]);

  // Subscription override computed properties
  const hasActiveOverride = useMemo(() => {
    return !!subscriptionOverride && subscriptionOverride.days_remaining > 0;
  }, [subscriptionOverride]);

  const effectiveTier = useMemo((): 'guest' | 'standard' | 'edu' | 'edu_pro' | 'pro' => {
    // If user has an active override, use the override tier
    if (hasActiveOverride && subscriptionOverride) {
      return subscriptionOverride.override_tier;
    }

    // Otherwise use the base account type
    if (isGuest) return 'guest';
    return accountType || 'standard';
  }, [hasActiveOverride, subscriptionOverride, isGuest, accountType]);

  const canAccessAdvancedAnalysis = useMemo(() => {
    // Guest users can access with sample data
    if (isGuest) {
      console.log('🔍 Advanced Analysis: Guest access granted');
      return true;
    }

    // Authenticated users need Pro OR Educational (free or paid) - check effective tier
    if (user) {
      const hasAccess = effectiveTier === 'pro' ||
                       effectiveTier === 'edu' ||
                       effectiveTier === 'edu_pro';
      console.log('🔍 Advanced Analysis Permission:', {
        userEmail: user.email,
        accountType,
        effectiveTier,
        hasActiveOverride,
        hasAccess,
        reason: hasAccess ? 'Effective tier allows access' : 'Effective tier does not allow access'
      });
      return hasAccess;
    }

    console.log('🔍 Advanced Analysis: No access - no user');
    return false;
  }, [isGuest, user, accountType, effectiveTier, hasActiveOverride]);

  const canAccessPublicationReady = useMemo(() => {
    // Guest users can access with sample data
    if (isGuest) return true;

    // Authenticated users need Pro OR Educational Pro (paid only) - check effective tier
    if (user) {
      const hasRegularPro = effectiveTier === 'pro';
      const hasEducationalPro = effectiveTier === 'edu_pro' || (effectiveTier === 'edu' && educationalTier === 'pro');

      console.log('🔍 Publication Ready Permission:', {
        userEmail: user.email,
        accountType,
        effectiveTier,
        educationalTier,
        hasActiveOverride,
        hasRegularPro,
        hasEducationalPro,
        hasAccess: hasRegularPro || hasEducationalPro
      });

      return hasRegularPro || hasEducationalPro;
    }

    return false;
  }, [isGuest, user, accountType, effectiveTier, educationalTier, hasActiveOverride]);

  const canAccessCloudStorage = useMemo(() => {
    // Only Pro users (regular or educational) get cloud storage - check effective tier
    if (user) {
      const hasRegularPro = effectiveTier === 'pro';
      const hasEducationalPro = effectiveTier === 'edu_pro' || (effectiveTier === 'edu' && educationalTier === 'pro');

      console.log('🔍 Cloud Storage Permission:', {
        userEmail: user.email,
        accountType,
        effectiveTier,
        educationalTier,
        hasActiveOverride,
        hasRegularPro,
        hasEducationalPro,
        hasAccess: hasRegularPro || hasEducationalPro
      });

      return hasRegularPro || hasEducationalPro;
    }

    return false;
  }, [user, accountType, effectiveTier, educationalTier, hasActiveOverride]);

  // Legacy property for backward compatibility
  // NOTE: This should be gradually replaced with granular permissions
  // For now, maintaining backward compatibility while allowing educational users access
  const canAccessProFeatures = canAccessAdvancedAnalysis;

  // Admin-related computed properties
  const canAccessAdminDashboard = useMemo(() => {
    // Only authenticated admin users can access admin dashboard
    return !!user && !isGuest && isAdmin;
  }, [user, isGuest, isAdmin]);

  // New computed properties for subscription management
  const hasActiveSubscription = subscriptionStatus === 'active';
  const nextPaymentDate = subscriptionData?.current_period_end || null;
  const billingCycle = subscriptionData?.billing_cycle || null;
  const canUpgradeAccount = !!user && (accountType === 'standard' || accountType === 'edu');

  // Function to clear the signup success message flag and session storage item
  const clearSignupSuccessMessage = useCallback(() => {
    setShowSignupSuccessMessage(false);
    sessionStorage.removeItem('showSignupSuccess'); // Clear from session storage
  }, []);

  // Function to fetch complete user profile data
  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      console.log('🔍 Fetching profile for user:', userId);
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('username, avatar_url, full_name, institution, country, accounttype, edu_subscription_type, is_admin')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error("❌ Error fetching profile:", profileError);
        return null;
      }

      console.log('✅ Profile fetched successfully:', profile);
      return profile as UserProfile;
    } catch (error) {
      console.error("❌ Failed to fetch user profile:", error);
      return null;
    }
  };

  // Function to refresh profile data
  const refreshProfile = useCallback(async () => {
    if (!user) {
      console.log('⚠️ No user available for profile refresh');
      return;
    }

    console.log('🔄 Refreshing profile for user:', user.id);
    const profile = await fetchUserProfile(user.id);
    setUserProfile(profile);

    // Update accountType from profile
    if (profile?.accounttype) {
      console.log('✅ Account type updated:', profile.accounttype);
      setAccountType(profile.accounttype);
    } else {
      console.log('⚠️ No account type found in profile, defaulting to standard');
      setAccountType('standard');
    }

    // Update educational tier from profile
    if (profile?.edu_subscription_type) {
      console.log('✅ Educational tier updated:', profile.edu_subscription_type);
      setEducationalTier(profile.edu_subscription_type);
    } else {
      console.log('⚠️ No educational tier found, setting to null');
      setEducationalTier(null);
    }

    // Update admin status from profile
    if (profile?.is_admin !== undefined) {
      console.log('✅ Admin status updated:', profile.is_admin);
      setIsAdmin(profile.is_admin);
    } else {
      console.log('⚠️ No admin status found in profile, defaulting to false');
      setIsAdmin(false);
    }

    // Permission calculations completed
  }, [user]);

  // Function to refresh subscription data
  const refreshSubscription = useCallback(async () => {
    if (!user || !accountType || (accountType !== 'pro' && accountType !== 'edu')) {
      setSubscriptionData(null);
      setSubscriptionStatus(null);
      return;
    }

    // Skip subscription calls when on admin dashboard to prevent conflicts
    const isOnAdminDashboard = location.pathname.includes('/admin-dashboard');
    if (isOnAdminDashboard) {
      console.log('⚠️ Skipping subscription refresh on admin dashboard');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        // Don't log 406 errors as they're expected when subscriptions table doesn't exist
        if (error.code !== '406' && !error.message?.includes('406')) {
          console.error('Error fetching subscription:', error);
        }
        return;
      }

      if (data) {
        setSubscriptionData(data);
        setSubscriptionStatus(data.status);
      } else {
        setSubscriptionData(null);
        setSubscriptionStatus(null);
      }
    } catch (error: any) {
      // Silently handle 406 errors and subscription-related errors
      if (!error.message?.includes('406') && !error.message?.includes('subscription')) {
        console.error('Error refreshing subscription:', error);
      }
    }
  }, [user, accountType]);

  // Function to refresh admin status
  const refreshAdminStatus = useCallback(async () => {
    if (!user) {
      setIsAdmin(false);
      return;
    }

    try {
      console.log('🔄 Refreshing admin status for user:', user.id);
      const { data, error } = await supabase.rpc('is_user_admin', { user_id: user.id });

      if (error) {
        console.error('❌ Error checking admin status:', error);
        setIsAdmin(false);
        return;
      }

      console.log('✅ Admin status refreshed:', data);
      setIsAdmin(data || false);
    } catch (error) {
      console.error('❌ Error refreshing admin status:', error);
      setIsAdmin(false);
    }
  }, [user]);

  // Function to refresh subscription override status
  const refreshOverrideStatus = useCallback(async () => {
    if (!user) {
      setSubscriptionOverride(null);
      return;
    }

    try {
      console.log('🔄 Refreshing override status for user:', user.id);
      const { data, error } = await supabase.rpc('get_user_active_override', {
        target_user_id: user.id
      });

      if (error) {
        console.error('❌ Error checking override status:', error);
        setSubscriptionOverride(null);
        return;
      }

      if (data && data.length > 0) {
        const override = data[0];
        console.log('✅ Override status refreshed:', override);
        setSubscriptionOverride(override);
      } else {
        console.log('✅ No active override found');
        setSubscriptionOverride(null);
      }
    } catch (error) {
      console.error('❌ Error refreshing override status:', error);
      setSubscriptionOverride(null);
    }
  }, [user]);

  // Fetch subscription data when user or account type changes
  // Skip subscription calls when on admin dashboard to prevent conflicts
  useEffect(() => {
    const isOnAdminDashboard = location.pathname.includes('/admin-dashboard');

    if (user && accountType && (accountType === 'pro' || accountType === 'edu') && !isOnAdminDashboard) {
      refreshSubscription();
    } else {
      setSubscriptionData(null);
      setSubscriptionStatus(null);
    }
  }, [user, accountType, refreshSubscription, location.pathname]);

  // Admin status is now handled directly in profile fetching
  // No separate refresh needed to prevent flashing

  // Refresh override status when user changes
  useEffect(() => {
    if (user) {
      refreshOverrideStatus();
    } else {
      setSubscriptionOverride(null);
    }
  }, [user, refreshOverrideStatus]);

  // Periodic refresh of override status to catch admin-created overrides
  // Enhanced with connection monitoring for admin users
  useEffect(() => {
    if (!user) return;

    // Initial refresh
    refreshOverrideStatus();

    // Set up periodic refresh every 30 seconds to catch new overrides
    // For admin users, refresh more frequently to maintain connection
    const refreshInterval = isAdmin ? 15000 : 30000; // 15s for admin, 30s for others
    
    const intervalId = setInterval(() => {
      console.log(`🔄 Periodic ${isAdmin ? 'admin' : 'user'} status refresh`);
      refreshOverrideStatus();
      
      // For admin users, also refresh profile to maintain Supabase connection
      if (isAdmin) {
        refreshProfile().catch(error => {
          console.error('❌ Admin profile refresh failed:', error);
          // If profile refresh fails, try to refresh the session
          supabase.auth.getSession().then(({ data: { session }, error: sessionError }) => {
            if (sessionError) {
              console.error('❌ Admin session refresh failed:', sessionError);
            } else if (session) {
              console.log('✅ Admin session refreshed successfully');
            }
          });
        });
      }
    }, refreshInterval);

    // Refresh when tab becomes visible (user switches back to tab)
    const handleVisibilityChange = () => {
      if (!document.hidden && user) {
        console.log('🔄 Tab visibility refresh - checking status');
        refreshOverrideStatus();
        
        // For admin users, also refresh profile on tab focus
        if (isAdmin) {
          refreshProfile().catch(error => {
            console.error('❌ Admin profile refresh on focus failed:', error);
          });
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(intervalId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, refreshOverrideStatus, isAdmin, refreshProfile]);

  // Comprehensive state cleanup function
  const clearAuthenticationState = useCallback((broadcastToOtherTabs: boolean = false) => {
    console.log('🧹 Clearing all authentication state...');

    try {
      // Broadcast logout signal to other tabs before clearing state
      if (broadcastToOtherTabs) {
        const logoutSignal = {
          timestamp: Date.now(),
          reason: 'logout'
        };
        localStorage.setItem('datastatpro-logout-signal', JSON.stringify(logoutSignal));

        // Remove the signal after a brief delay to prevent it from persisting
        setTimeout(() => {
          localStorage.removeItem('datastatpro-logout-signal');
        }, 1000);
      }

      // Clear React state
      setSession(null);
      setUser(null);
      setIsGuest(false);
      setUserProfile(null);
      setAccountType(null);
      setIsAdmin(false);
      setSubscriptionData(null);
      setSubscriptionStatus(null);
      setEducationalTier(null);
      setIsLoggingOut(false); // Reset logout state

      // Clear session storage
      sessionStorage.removeItem('isGuest');
      sessionStorage.removeItem('showSignupSuccess');

      // Clear any auth-related localStorage flags
      localStorage.removeItem('datastatpro-auth-loading-stuck');

      console.log('✅ Authentication state cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing authentication state:', error);
    }
  }, []);

  // Cross-tab authentication synchronization
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      // Handle cross-tab logout synchronization
      if (event.key === 'datastatpro-logout-signal' && event.newValue) {
        console.log('🔄 Cross-tab logout signal received');
        const logoutData = JSON.parse(event.newValue);

        // Only process if this is a different tab (different timestamp)
        if (logoutData.timestamp && Date.now() - logoutData.timestamp < 5000) {
          console.log('🔄 Synchronizing logout across tabs');
          clearAuthenticationState();

          // Navigate to login screen if not already there
          if (!location.pathname.includes('/auth')) {
            navigate('/auth/login');
          }
        }
      }

      // Handle cross-tab login synchronization
      if (event.key === 'datastatpro-login-signal' && event.newValue) {
        console.log('🔄 Cross-tab login signal received');
        // Refresh the current session to sync with other tabs
        supabase.auth.getSession().then(({ data: { session } }) => {
          if (session && !user) {
            console.log('🔄 Syncing login state from other tab');
            // Broadcast login signal to maintain session persistence
            const loginSignal = {
              timestamp: Date.now(),
              userId: session.user.id
            };
            localStorage.setItem('datastatpro-session-sync', JSON.stringify(loginSignal));
            setTimeout(() => {
              localStorage.removeItem('datastatpro-session-sync');
            }, 1000);
          }
        });
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [user, clearAuthenticationState, navigate]);

  // Initialize production Firefox auth fix
  // TEMPORARILY DISABLED: Firefox fixes are causing authentication issues
  // useEffect(() => {
  //   productionFirefoxAuthFix.initialize();
  //
  //   return () => {
  //     productionFirefoxAuthFix.cleanup();
  //   };
  // }, []);

  useEffect(() => {
    // Set up authentication loading timeout to detect stuck states
    const authLoadingTimeout = setTimeout(() => {
      if (loading) {
        console.warn('Authentication loading timeout - marking for cache recovery');
        localStorage.setItem('datastatpro-auth-loading-stuck', 'true');
        setLoading(false); // Force loading to false to prevent infinite loading
      }
    }, 10000); // 10 second timeout

    // Check for guest status in session storage on initial load
    const guestStatus = sessionStorage.getItem('isGuest');
    if (guestStatus === 'true') {
      clearTimeout(authLoadingTimeout);
      setIsGuest(true);
      setUser(null);
      setSession(null);
      setLoading(false);
      return;
    }

    // Check for signup success message in session storage on initial load
    const signupSuccess = sessionStorage.getItem('showSignupSuccess');
    if (signupSuccess === 'true') {
      setShowSignupSuccessMessage(true);
      // Do NOT clear it here, let App.tsx clear it after displaying
    }

    supabase.auth.getSession().then(async ({ data: { session: currentSession } }) => {
      try {
        clearTimeout(authLoadingTimeout); // Clear timeout on successful session retrieval
        localStorage.removeItem('datastatpro-auth-loading-stuck'); // Clear stuck flag

        setSession(currentSession);
        const currentUser = currentSession?.user ?? null;
        setUser(currentUser);
        setIsGuest(false);

        // Fetch complete profile data if user exists
        if (currentUser) {
          console.log('🔍 Initial session: Fetching profile for user:', currentUser.id);
          try {
            const profile = await fetchUserProfile(currentUser.id);
            setUserProfile(profile);

            if (profile?.accounttype) {
              setAccountType(profile.accounttype);
            } else {
              setAccountType('standard'); // Default to standard if no account type
            }

            // Set educational tier
            if (profile?.edu_subscription_type) {
              setEducationalTier(profile.edu_subscription_type);
            } else {
              setEducationalTier(null);
            }

            // Set admin status
            if (profile?.is_admin !== undefined) {
              setIsAdmin(profile.is_admin);
            } else {
              setIsAdmin(false);
            }
          } catch (profileError) {
            console.error('🔍 Error fetching profile during initial session:', profileError);
            // Set default values if profile fetch fails
            setUserProfile(null);
            setAccountType('standard');
            setEducationalTier(null);
            setIsAdmin(false);
          }
        } else {
          setUserProfile(null);
          setAccountType(null);
          setEducationalTier(null);
          setIsAdmin(false);
        }

        setLoading(false);

        // Log 'app_open' if a user is already authenticated on initial load
        if (currentUser) {
          try {
            logLoginEvent('app_open');
          } catch (loginEventError) {
            console.error('🔍 Error logging initial app_open event:', loginEventError);
            // Don't fail the session loading for logging errors
          }
        }
      } catch (sessionError) {
        console.error('🔍 Error processing initial session:', sessionError);
        console.error('🔍 Session error stack:', sessionError instanceof Error ? sessionError.stack : 'No stack trace');

        // Set safe defaults on error
        setLoading(false);
        if (currentSession) {
          setSession(currentSession);
          setUser(currentSession.user);
        }
      }
    }).catch((error) => {
      clearTimeout(authLoadingTimeout); // Clear timeout on error
      console.error('Failed to get session:', error);
      localStorage.setItem('datastatpro-auth-loading-stuck', 'true');
      setLoading(false); // Set loading to false even on error
    });

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, newSession) => { // Made async
        if (_event === 'SIGNED_OUT') {
          console.log('🔄 SIGNED_OUT event received, clearing state...');
          
          // Use centralized state clearing function and broadcast to other tabs
          clearAuthenticationState(true);
          
          // Only navigate if not already in a logout process to prevent race conditions
          if (!isLoggingOut) {
            console.log('🔄 Navigating to login screen after signout');
            navigate('/auth/login'); // Navigate to login screen instead of auth landing
          } else {
            console.log('⚠️ Skipping navigation - logout already in progress');
          }
        } else if (newSession) {
          setIsGuest(false);
          sessionStorage.removeItem('isGuest');

          // Check for signup confirmation in URL search params
          const searchParams = new URLSearchParams(location.search);
          const type = searchParams.get('type');

          if (type === 'signup' && newSession.user) {
            setShowSignupSuccessMessage(true);
            sessionStorage.setItem('showSignupSuccess', 'true'); // Persist across refreshes

            // Clean up URL search parameters
            searchParams.delete('type');
            const newSearch = searchParams.toString();
            const newUrl = `${window.location.pathname}${newSearch ? '?' + newSearch : ''}${window.location.hash}`;
            window.history.replaceState({}, document.title, newUrl);
          }
        }
        setSession(newSession);
        const currentUser = newSession?.user ?? null;
        setUser(currentUser);

        // Fetch complete profile data if user exists (consistent with initial session loading)
        if (currentUser) {
          console.log('🔍 Auth state change: Fetching complete profile for user:', currentUser.id);
          try {
            const profile = await fetchUserProfile(currentUser.id);
            setUserProfile(profile);

            if (profile?.accounttype) {
              setAccountType(profile.accounttype);
            } else {
              setAccountType('standard'); // Default to standard if no account type
            }

            // Set educational tier
            if (profile?.edu_subscription_type) {
              setEducationalTier(profile.edu_subscription_type);
            } else {
              setEducationalTier(null);
            }

            // Set admin status
            if (profile?.is_admin !== undefined) {
              setIsAdmin(profile.is_admin);
            } else {
              setIsAdmin(false);
            }
          } catch (profileError) {
            console.error('🔍 Error fetching profile during auth state change:', profileError);
            // Set default values if profile fetch fails
            setUserProfile(null);
            setAccountType('standard');
            setEducationalTier(null);
            setIsAdmin(false);
          }
        } else {
          setUserProfile(null);
          setAccountType(null);
          setEducationalTier(null);
          setIsAdmin(false);
        }
        setLoading(false);

        // Log 'app_open' when a user signs in
        if (_event === 'SIGNED_IN' && currentUser) {
          logLoginEvent('app_open');
        }
      }
    );

    return () => {
      clearTimeout(authLoadingTimeout); // Clean up timeout
      subscription.unsubscribe();
    };
  }, [location.pathname, location.search, navigate, isLoggingOut, clearAuthenticationState]); // Updated dependencies



  // Effect to handle navigation after successful sign-in
  useEffect(() => {
    // Check if the user is authenticated and not a guest
    // And if the current path indicates they are on an auth-related page or OAuth callback
    const currentPath = location.pathname;
    const isAuthPage = currentPath.startsWith('/auth/login') || currentPath.startsWith('/auth/register') || currentPath.startsWith('/auth/reset-password');
    const isOAuthCallback = currentPath === '/app/dashboard' && location.hash.includes('access_token');
    
    // Handle OAuth callback - wait for user to be set, then stay on dashboard
    if (isOAuthCallback && !user) {
      console.log('🔄 OAuth callback detected, waiting for authentication...');
      return;
    }
    
    // If user is authenticated after OAuth callback, ensure they stay on dashboard
    if (isOAuthCallback && user && !isGuest) {
      console.log('✅ OAuth authentication successful, user is now on dashboard');
      return;
    }

    // Navigate from auth pages to dashboard after regular sign-in
    if (user && !isGuest && isAuthPage) {
      navigate('/app/dashboard');
    }
  }, [user, isGuest, location.pathname, location.hash, navigate]);

  // REMOVED: Firefox fallback authentication - was causing authentication issues

  const signIn = async (email: string, password: string) => {
    const { error, data } = await supabase.auth.signInWithPassword({ email, password });

    if (!error) {
      // Check if user email is confirmed
      if (data.user && data.user.confirmed_at) {
        setIsGuest(false);
        sessionStorage.removeItem('isGuest');
        
        // Broadcast login signal for cross-tab synchronization
        const loginSignal = {
          timestamp: Date.now(),
          userId: data.user.id
        };
        localStorage.setItem('datastatpro-login-signal', JSON.stringify(loginSignal));
        setTimeout(() => {
          localStorage.removeItem('datastatpro-login-signal');
        }, 1000);
        
        return { error: null };
      } else {
        // User exists but email is not confirmed
        await supabase.auth.signOut();
        return { error: { message: "Please confirm your email before signing in" } };
      }
    }
    return { error };
  };

  const signUp = async (email: string, password: string, options?: { data?: Record<string, any> }) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: options?.data,
        emailRedirectTo: window.location.origin,
      }
    });
    return { error, user: data?.user || null };
  };

  const signInWithGoogle = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/app/dashboard`, // Redirect to dashboard after Google login
      },
    });
    
    if (!error) {
      // Broadcast login signal for cross-tab synchronization
      const loginSignal = {
        timestamp: Date.now(),
        provider: 'google'
      };
      localStorage.setItem('datastatpro-login-signal', JSON.stringify(loginSignal));
      setTimeout(() => {
        localStorage.removeItem('datastatpro-login-signal');
      }, 1000);
    }
    
    return { error };
  };

  const signInWithMagicLink = async (email: string) => {
    try {
      console.log('🔐 Starting magic link sign-in for:', email);
      
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/app/dashboard`
        }
      });

      if (error) {
        console.error('❌ Magic link sign-in error:', error);
        return { error };
      }

      console.log('✅ Magic link sent successfully to:', email);
      return { error: null };
    } catch (error) {
      console.error('❌ Unexpected error during magic link sign-in:', error);
      return { error };
    }
  };

  const signOut = async (retryAttempt: number = 0) => {
    // Prevent multiple simultaneous logout attempts
    if (isLoggingOut && retryAttempt === 0) {
      console.log('⚠️ Logout already in progress, skipping duplicate request');
      return;
    }

    const maxRetries = 2;
    const isRetry = retryAttempt > 0;

    try {
      console.log(`🔄 ${isRetry ? `Retrying logout (attempt ${retryAttempt + 1}/${maxRetries + 1})` : 'Starting logout process'}...`);
      
      setIsLoggingOut(true);
      
      // Stop session heartbeat monitoring
      console.log('🔐 Stopping session heartbeat monitoring');
      sessionManager.stopHeartbeatMonitoring();

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Logout timeout')), 8000);
      });

      const logoutPromise = supabase.auth.signOut();
      await Promise.race([logoutPromise, timeoutPromise]);

      console.log('✅ Supabase logout successful');
      
      // Clear authentication state and broadcast to other tabs
      clearAuthenticationState(true);
      
      // Navigate to login screen instead of auth landing page
      navigate('/auth/login');
      
    } catch (error) {
      console.error(`❌ Logout error (attempt ${retryAttempt + 1}):`, error);
      
      if (retryAttempt < maxRetries) {
        console.log(`🔄 Retrying logout in 1 second...`);
        setTimeout(() => signOut(retryAttempt + 1), 1000);
        return;
      }
      
      // Final fallback: manually clear state and navigate
      console.log('🔄 Final fallback: manually clearing state and navigating');
      clearAuthenticationState(true);
      navigate('/auth/login');
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Clear all dataset-related local storage for Guest login security
  const clearDatasetStorage = () => {
    try {
      // Clear main dataset storage (both new and legacy keys)
      localStorage.removeItem('datastatpro_datasets');
      localStorage.removeItem('statistica_datasets');

      // Clear results and projects storage (both new and legacy keys)
      localStorage.removeItem('datastatpro_results');
      localStorage.removeItem('statistica_results');
      localStorage.removeItem('datastatpro_projects');
      localStorage.removeItem('statistica_projects');

      // Clear analysis configuration and results storage
      const analysisKeys = [
        'descriptive_analysis_config',
        'descriptive_analysis_results',
        'cross_tabulation_config',
        'cross_tabulation_results',
        'linear_regression_results',
        'posthoc_test_results',
        'mediation_results',
        'moderation_results',
        'cohort_calculator_cell_values',
        'cohort_calculator_strata',
        'cohort_calculator_results',
        'cohort_calculator_current_stratum_index',
        'analysisAssistantTrainingData'
      ];

      // Clear t-test results for all test types
      const ttestTypes = ['one_sample', 'independent', 'paired'];
      ttestTypes.forEach(type => {
        analysisKeys.push(`ttest_results_${type}`);
        analysisKeys.push(`ttest_assumptions_${type}`);
      });

      // Clear guided workflow progress data
      Object.keys(localStorage).forEach(key => {
        if (key.endsWith('-progress')) {
          analysisKeys.push(key);
        }
      });

      // Remove all analysis-related keys
      analysisKeys.forEach(key => {
        localStorage.removeItem(key);
      });

      console.log('🧹 Cleared all dataset-related storage for Guest login');
    } catch (error) {
      console.error('Error clearing dataset storage:', error);
    }
  };

  const loginAsGuest = () => {
    setLoading(true);

    // Stop session heartbeat monitoring
    console.log('🔐 Stopping session heartbeat monitoring for guest login');
    sessionManager.stopHeartbeatMonitoring();

    // Clear all dataset-related storage to prevent privilege escalation
    clearDatasetStorage();

    setSession(null);
    setUser(null);
    setIsGuest(true);
    sessionStorage.setItem('isGuest', 'true');
    setLoading(false);
  };

  // Simplified guest access without email verification
  const loginAsGuestDirect = () => {
    setLoading(true);
    console.log('🔄 Logging in as guest (direct access)');

    // Stop session heartbeat monitoring
    sessionManager.stopHeartbeatMonitoring();

    // Clear all dataset-related storage to prevent privilege escalation
    clearDatasetStorage();

    // Create a temporary guest user object for immediate access
    const tempGuestUser: GuestUser = {
      id: `temp-guest-${Date.now()}`,
      email: '<EMAIL>',
      full_name: 'Guest User',
      institution: null,
      status: 'verified', // Mark as verified for immediate access
      verification_code: null,
      verification_expires_at: undefined,
      created_at: new Date(),
      updated_at: new Date()
    };

    setSession(null);
    setUser(null);
    setGuestUser(tempGuestUser);
    setIsGuest(true);
    sessionStorage.setItem('isGuest', 'true');
    sessionStorage.setItem('guestUser', JSON.stringify(tempGuestUser));
    
    setLoading(false);
    console.log('✅ Guest direct access granted');
  };

  // Function to log login events only to the 'events' table
  const logLoginEvent = async (event_type: 'app_open' | 'login' | 'signin' | 'sign_in' | 'signed_in', details?: Record<string, any>) => {
    try {
      const currentUser = await supabase.auth.getUser();
      const userId = currentUser.data.user?.id;

      if (!userId) {
        console.warn("Attempted to log login event without a user_id:", event_type);
        return;
      }

      // Validate that only login-related events are logged
      const allowedEvents = ['app_open', 'login', 'signin', 'sign_in', 'signed_in'];
      if (!allowedEvents.includes(event_type)) {
        console.warn("Attempted to log non-login event:", event_type);
        return;
      }

      const { error } = await supabase
        .from('events')
        .insert([
          { user_id: userId, event_type, details }
        ]);

      if (error) {
        console.error("Error logging login event:", error);
      } else {
        console.log(`✅ Login event logged: ${event_type}`);
      }
    } catch (error) {
      console.error("Unexpected error logging login event:", error);
    }
  };

  const logoutGuest = () => {
    setLoading(true);
    setIsGuest(false);
    sessionStorage.removeItem('isGuest');
    setLoading(false);
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    return { error };
  };

  const updateProfile = async (data: { username?: string, avatar_url?: string, full_name?: string, institution?: string, country?: string }) => {
    if (!user) return { error: { message: "User not authenticated" } };

    console.log('🔄 Updating profile for user:', user.id, data);
    const { error } = await supabase
      .from('profiles')
      .update({
        ...data,
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);

    if (!error) {
      console.log('✅ Profile updated successfully, refreshing profile data...');
      // Refresh profile data after successful update
      await refreshProfile();
    } else {
      console.error('❌ Error updating profile:', error);
    }

    return { error };
  };

  const uploadAvatar = async (file: File) => {
    if (!user) return { error: { message: "User not authenticated" } };

    const MAX_FILE_SIZE = 100 * 1024;
    if (file.size > MAX_FILE_SIZE) {
      return { error: { message: "Avatar image must be less than 100KB in size." } };
    }

    const fileExt = file.name.split('.').pop();
    const fileName = `${user.id}.${fileExt}`;
    const filePath = `${user.id}/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(filePath, file, { upsert: true });

    if (uploadError) {
      return { error: uploadError };
    }

    const { data: { publicUrl } } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);

    if (!publicUrl) {
      return { error: { message: "Could not get public URL for avatar." } };
    }
    
    const { error: updateError } = await updateProfile({ avatar_url: publicUrl });
    if (updateError) {
      return { error: updateError };
    }

    return { error: null, publicUrl };
  };

  const forceRefreshUser = async (userId: string) => {
    if (!isAdmin) {
      return { error: { message: 'You do not have permission to perform this action.' } };
    }

    try {
      const { error } = await supabase.rpc('admin_refresh_user_session', { target_user_id: userId });

      if (error) {
        console.error('Error forcing user session refresh:', error);
        return { error };
      }

      console.log('Successfully triggered session refresh for user:', userId);
      return { error: null };
    } catch (error) {
      console.error('Unexpected error during session refresh:', error);
      return { error };
    }
  };

  // Guest user management functions
  const createGuestUser = async (request: GuestUserCreateRequest): Promise<GuestUserResponse> => {
    try {
      console.log('🔄 Creating guest user for email:', request.email);
      
      const { data, error } = await supabase.rpc('create_guest_user_v2', {
        p_email: request.email,
        p_full_name: request.fullName || null,
        p_institution: request.institution || null
      });

      if (error) {
        console.error('❌ Error creating guest user:', error);
        return {
          success: false,
          error: error.message || 'Failed to create guest user',
          guestUser: null
        };
      }

      console.log('✅ Guest user created successfully:', data);
      
      // Handle the v2 database function response structure
      if (data && Array.isArray(data) && data.length > 0) {
        const result = data[0];
        
        // Log email sending status for debugging
        if (result.email_sent) {
          console.log('📧 Verification email sent successfully');
        } else if (result.email_error) {
          console.warn('⚠️ Email sending failed:', result.email_error);
        }
        
        const newGuestUser: GuestUser = {
          id: result.guest_id,
          email: request.email,
          email_verified: false,
          verification_token: result.verification_token,
          login_count: 0,
          created_at: new Date(),
          updated_at: new Date(),
          status: 'unverified'
        };
        
        setGuestUser(newGuestUser);
        
        return {
          success: result.success,
          error: result.success ? null : (result.email_error ? `User created but email failed: ${result.email_error}` : result.message),
          guestUser: result.success ? newGuestUser : null,
          emailSent: result.email_sent
        };
      }
      
      return {
        success: false,
        error: 'Invalid response from server',
        guestUser: null
      };
    } catch (error) {
      console.error('❌ Unexpected error creating guest user:', error);
      return {
        success: false,
        error: 'An unexpected error occurred',
        guestUser: null
      };
    }
  };

  const verifyGuestEmail = async (request: GuestUserVerifyRequest): Promise<GuestUserResponse> => {
    try {
      console.log('🔄 Verifying guest email with code:', request.verificationCode);
      
      const { data, error } = await supabase.rpc('verify_guest_email', {
        p_email: request.email,
        p_verification_code: request.verificationCode
      });

      if (error) {
        console.error('❌ Error verifying guest email:', error);
        return {
          success: false,
          error: error.message || 'Failed to verify email',
          guestUser: null
        };
      }

      console.log('✅ Email verification response:', data);
      
      // Handle the new database function response structure
      if (data && Array.isArray(data) && data.length > 0) {
        const result = data[0];
        
        if (!result.success) {
          console.log('❌ Email verification failed:', result.message);
          return {
            success: false,
            error: result.message || 'Email verification failed',
            guestUser: null
          };
        }

        // Fetch the updated guest user data
        const { data: guestData, error: fetchError } = await supabase
          .from('guest_users')
          .select('*')
          .eq('id', result.guest_id)
          .single();

        if (fetchError || !guestData) {
          console.error('❌ Error fetching verified guest user:', fetchError);
          return {
            success: false,
            error: 'Failed to fetch user data after verification',
            guestUser: null
          };
        }

        const verifiedGuestUser: GuestUser = {
          ...guestData,
          created_at: new Date(guestData.created_at),
          updated_at: new Date(guestData.updated_at),
          verification_expires_at: guestData.verification_expires_at ? new Date(guestData.verification_expires_at) : undefined,
          first_login_at: guestData.first_login_at ? new Date(guestData.first_login_at) : undefined,
          last_login_at: guestData.last_login_at ? new Date(guestData.last_login_at) : undefined
        };

        setGuestUser(verifiedGuestUser);
        
        return {
          success: true,
          error: null,
          guestUser: verifiedGuestUser
        };
      }
      
      return {
        success: false,
        error: 'Invalid response from server',
        guestUser: null
      };
    } catch (error) {
      console.error('❌ Unexpected error verifying guest email:', error);
      return {
        success: false,
        error: 'An unexpected error occurred',
        guestUser: null
      };
    }
  };

  const loginAsEmailVerifiedGuest = async (request: GuestUserLoginRequest): Promise<GuestUserResponse> => {
    try {
      console.log('🔄 Logging in as email-verified guest:', request.email);
      
      const { data, error } = await supabase.rpc('get_guest_user_by_email', {
        p_email: request.email
      });

      if (error) {
        console.error('❌ Error getting guest user:', error);
        return {
          success: false,
          error: error.message || 'Failed to login as guest',
          guestUser: null
        };
      }

      if (!data || data.status !== 'verified') {
        return {
          success: false,
          error: 'Guest user not found or not verified',
          guestUser: null
        };
      }

      console.log('✅ Email-verified guest login successful:', data);
      const guestUserData = data as GuestUser;
      
      // Update login timestamp
      await supabase.rpc('update_guest_login', {
        p_guest_user_id: guestUserData.id
      });
      
      // Set guest user state
      setGuestUser(guestUserData);
      setIsGuest(true);
      setUser(null);
      setSession(null);
      sessionStorage.setItem('isGuest', 'true');
      sessionStorage.setItem('guestUserId', guestUserData.id);
      
      // Clear dataset storage for security
      clearDatasetStorage();
      
      return {
        success: true,
        error: null,
        guestUser: guestUserData
      };
    } catch (error) {
      console.error('❌ Unexpected error logging in as email-verified guest:', error);
      return {
        success: false,
        error: 'An unexpected error occurred',
        guestUser: null
      };
    }
  };

  const startGuestAnalyticsSession = async (): Promise<void> => {
    if (!guestUser) {
      console.warn('⚠️ Cannot start analytics session: no guest user');
      return;
    }

    try {
      console.log('🔄 Starting guest analytics session for:', guestUser.id);
      
      const { data, error } = await supabase.rpc('start_guest_analytics_session', {
        p_guest_user_id: guestUser.id
      });

      if (error) {
        console.error('❌ Error starting guest analytics session:', error);
        return;
      }

      console.log('✅ Guest analytics session started:', data);
      setGuestAnalyticsSession(data as GuestAnalyticsSession);
    } catch (error) {
      console.error('❌ Unexpected error starting guest analytics session:', error);
    }
  };

  const endGuestAnalyticsSession = async (): Promise<void> => {
    if (!guestAnalyticsSession) {
      console.warn('⚠️ Cannot end analytics session: no active session');
      return;
    }

    try {
      console.log('🔄 Ending guest analytics session:', guestAnalyticsSession.id);
      
      const { error } = await supabase.rpc('end_guest_analytics_session', {
        p_session_id: guestAnalyticsSession.id
      });

      if (error) {
        console.error('❌ Error ending guest analytics session:', error);
        return;
      }

      console.log('✅ Guest analytics session ended');
      setGuestAnalyticsSession(null);
    } catch (error) {
      console.error('❌ Unexpected error ending guest analytics session:', error);
    }
  };

  const logoutEmailVerifiedGuest = (): void => {
    console.log('🔄 Logging out email-verified guest');
    
    // End analytics session if active
    if (guestAnalyticsSession) {
      endGuestAnalyticsSession();
    }
    
    // Clear guest user state
    setGuestUser(null);
    setGuestAnalyticsSession(null);
    setIsGuest(false);
    
    // Clear session storage
    sessionStorage.removeItem('isGuest');
    sessionStorage.removeItem('guestUserId');
    
    // Clear dataset storage for security
    clearDatasetStorage();
    
    console.log('✅ Email-verified guest logged out');
  };

  // Session manager integration - placed after all functions are defined
  useEffect(() => {
    console.log('🔐 Setting up session manager integration');
    
    // Set up session validation callback
    const unsubscribeSessionValidation = sessionManager.onSessionValidation((isValid) => {
      console.log('🔐 Session validation callback:', isValid ? 'Valid' : 'Invalid');
      
      if (!isValid && user && !isLoggingOut) {
        console.warn('🔐 Session became invalid, initiating logout...');
        // Session became invalid, trigger logout
        signOut().catch(error => {
          console.error('🔐 Failed to sign out after session invalidation:', error);
        });
      }
    });

    // Start heartbeat monitoring if user is already signed in
    if (user && !isGuest) {
      console.log('🔐 User is signed in, ensuring heartbeat monitoring is active');
      sessionManager.startHeartbeatMonitoring();
    }

    return () => {
      console.log('🔐 Cleaning up session manager integration');
      unsubscribeSessionValidation();
    };
  }, [user, isGuest, isLoggingOut, signOut]);

  const value = {
    session,
    user,
    userProfile,
    isGuest,
    isAuthenticated: !!user && !isGuest,
    loading,
    canAccessSampleData,
    canImportData,
    canAccessProFeatures,
    showSignupSuccessMessage, // Provide new state
    clearSignupSuccessMessage, // Provide new function
    signIn,
    signUp,
    signInWithGoogle, // Provide new function
    signInWithMagicLink, // Provide magic link function
    signOut,
    loginAsGuest,
    loginAsGuestDirect,
    logoutGuest,
    resetPassword,
    updateProfile,
    uploadAvatar,
    logLoginEvent, // Add logLoginEvent to the value object (renamed from logEvent for login-only tracking)
    accountType,
    refreshProfile, // Add refreshProfile function
    // New subscription-related properties
    subscriptionData,
    subscriptionStatus,
    hasActiveSubscription,
    nextPaymentDate,
    billingCycle,
    refreshSubscription,
    canUpgradeAccount,
    // New educational tier properties
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    canAccessCloudStorage,
    isEducationalUser,
    educationalTier,
    // Admin-related properties
    isAdmin,
    canAccessAdminDashboard,
    refreshAdminStatus,
    // Subscription override properties
    subscriptionOverride,
    effectiveTier,
    hasActiveOverride,
    refreshOverrideStatus,
    forceRefreshUser,
    // Guest user management properties
    guestUser,
    guestAnalyticsSession,
    isEmailVerifiedGuest,
    createGuestUser,
    verifyGuestEmail,
    loginAsEmailVerifiedGuest,
    startGuestAnalyticsSession,
    endGuestAnalyticsSession,
    logoutEmailVerifiedGuest,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
