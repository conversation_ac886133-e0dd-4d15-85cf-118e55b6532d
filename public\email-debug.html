<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email System Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1565c0;
        }
        input[type="email"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Email System Debug Tool</h1>
        <p>This tool helps debug the Email Guest verification system.</p>
        
        <div class="test-section">
            <h3>Configuration</h3>
            <label>Test Email: <input type="email" id="testEmail" value="<EMAIL>" placeholder="Enter your email"></label>
            <br>
            <button onclick="updateConfig()">Update Configuration</button>
            <div id="configResult" class="result info" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>1. Database Diagnostics</h3>
            <p>Check if the database is properly configured for email sending.</p>
            <button onclick="testDatabaseDiagnostics()">Run Database Diagnostics</button>
            <div id="diagnosticsResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Guest User Creation Test</h3>
            <p>Test the complete guest user creation and email sending flow.</p>
            <button onclick="testGuestUserCreation()">Test Guest User Creation</button>
            <div id="guestCreationResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Edge Function Direct Test</h3>
            <p>Test the Edge Function directly to verify email sending capability.</p>
            <button onclick="testEdgeFunctionDirect()">Test Edge Function</button>
            <div id="edgeFunctionResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Database Configuration Test</h3>
            <p>Test database configuration parameters and HTTP extension.</p>
            <button onclick="testDatabaseConfiguration()">Test Database Config</button>
            <div id="dbConfigResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. Email Function Direct Test</h3>
            <p>Test the send_guest_verification_email_v2 function directly.</p>
            <button onclick="testEmailFunctionDirect()">Test Email Function</button>
            <div id="emailFunctionResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>6. Run All Tests</h3>
            <p>Run all tests in sequence to get a complete picture.</p>
            <button onclick="runAllTests()">Run All Tests</button>
            <div id="allTestsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // Configuration
        const SUPABASE_URL = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
        
        let TEST_EMAIL = '<EMAIL>';
        
        const { createClient } = supabase;
        const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function updateConfig() {
            TEST_EMAIL = document.getElementById('testEmail').value;
            const result = document.getElementById('configResult');
            result.style.display = 'block';
            result.className = 'result info';
            result.textContent = `Configuration updated!\nTest Email: ${TEST_EMAIL}`;
        }
        
        async function testDatabaseDiagnostics() {
            const button = event.target;
            const result = document.getElementById('diagnosticsResult');
            
            button.disabled = true;
            result.style.display = 'block';
            result.className = 'result info';
            result.textContent = 'Running database diagnostics...';
            
            try {
                const { data, error } = await supabaseClient.rpc('diagnose_email_system_v2');
                
                if (error) {
                    result.className = 'result error';
                    result.textContent = `❌ Database diagnostics failed: ${error.message}`;
                    return false;
                }
                
                result.className = 'result success';
                result.textContent = '📊 Diagnostic Results:\n' + JSON.stringify(data, null, 2);
                
                // Check for issues
                const issues = data.filter(item => 
                    item.status !== 'ENABLED' && 
                    item.status !== 'CONFIGURED' && 
                    item.status !== 'REACHABLE'
                );
                
                if (issues.length === 0) {
                    result.textContent += '\n\n✅ All database components are properly configured!';
                } else {
                    result.textContent += '\n\n⚠️ Issues found:\n' + 
                        issues.map(issue => `- ${issue.component}: ${issue.status} - ${issue.action_required}`).join('\n');
                }
                
                return issues.length === 0;
                
            } catch (err) {
                result.className = 'result error';
                result.textContent = `❌ Diagnostic test failed: ${err.message}`;
                return false;
            } finally {
                button.disabled = false;
            }
        }
        
        async function testGuestUserCreation() {
            const button = event.target;
            const result = document.getElementById('guestCreationResult');

            if (TEST_EMAIL === '<EMAIL>') {
                result.style.display = 'block';
                result.className = 'result error';
                result.textContent = '⚠️ Please update the test email address first!';
                return false;
            }

            button.disabled = true;
            result.style.display = 'block';
            result.className = 'result info';
            result.textContent = 'Testing guest user creation...\n';

            try {
                // First, clean up any existing test user
                result.textContent += '🧹 Cleaning up existing test user...\n';
                try {
                    await supabaseClient.from('guest_users').delete().eq('email', TEST_EMAIL);
                    result.textContent += '✅ Cleanup completed\n\n';
                } catch (cleanupError) {
                    result.textContent += `⚠️ Cleanup warning: ${cleanupError.message}\n\n`;
                }

                result.textContent += '👤 Creating guest user...\n';
                const { data, error } = await supabaseClient.rpc('create_guest_user_v3', {
                    p_email: TEST_EMAIL,
                    p_full_name: 'Test User',
                    p_institution: 'Email Test',
                    p_marketing_consent: false
                });

                if (error) {
                    result.className = 'result error';
                    result.textContent += `❌ Guest user creation failed: ${error.message}\n`;
                    result.textContent += `Error details: ${JSON.stringify(error, null, 2)}`;
                    return false;
                }

                result.textContent += '📧 Guest User Creation Results:\n';
                result.textContent += JSON.stringify(data, null, 2) + '\n\n';

                if (data && data.length > 0) {
                    const resultData = data[0];
                    result.textContent += `📊 Analysis:\n`;
                    result.textContent += `- Guest ID: ${resultData.guest_id}\n`;
                    result.textContent += `- Verification Token: ${resultData.verification_token}\n`;
                    result.textContent += `- Success: ${resultData.success}\n`;
                    result.textContent += `- Email Sent: ${resultData.email_sent}\n`;
                    result.textContent += `- Message: ${resultData.message}\n`;
                    if (resultData.email_error) {
                        result.textContent += `- Email Error: ${resultData.email_error}\n`;
                    }

                    if (resultData.success && resultData.email_sent) {
                        result.className = 'result success';
                        result.textContent += `\n✅ Guest user created and email sent successfully!\n📬 Check ${TEST_EMAIL} for the verification code: ${resultData.verification_token}`;
                        return true;
                    } else {
                        result.className = 'result error';
                        result.textContent += `\n⚠️ Guest user created but email failed!`;

                        // Additional debugging: Test the email function directly
                        if (resultData.guest_id && resultData.verification_token) {
                            result.textContent += '\n\n🔍 Testing email function directly...\n';
                            try {
                                const emailTest = await supabaseClient.rpc('send_guest_verification_email_v3', {
                                    p_email: TEST_EMAIL,
                                    p_verification_code: resultData.verification_token,
                                    p_guest_id: resultData.guest_id
                                });

                                result.textContent += 'Direct email test results:\n';
                                result.textContent += JSON.stringify(emailTest, null, 2);
                            } catch (emailTestError) {
                                result.textContent += `Direct email test failed: ${emailTestError.message}`;
                            }
                        }

                        return false;
                    }
                } else {
                    result.className = 'result error';
                    result.textContent += '❌ No data returned from guest user creation';
                    return false;
                }

            } catch (err) {
                result.className = 'result error';
                result.textContent += `❌ Guest user creation test failed: ${err.message}\n`;
                result.textContent += `Stack trace: ${err.stack}`;
                return false;
            } finally {
                button.disabled = false;
            }
        }
        
        async function testEdgeFunctionDirect() {
            const button = event.target;
            const result = document.getElementById('edgeFunctionResult');
            
            if (TEST_EMAIL === '<EMAIL>') {
                result.style.display = 'block';
                result.className = 'result error';
                result.textContent = '⚠️ Please update the test email address first!';
                return false;
            }
            
            button.disabled = true;
            result.style.display = 'block';
            result.className = 'result info';
            result.textContent = 'Testing Edge Function directly...';
            
            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/send-guest-verification-email`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: TEST_EMAIL,
                        verificationCode: '123456',
                        guestId: '00000000-0000-0000-0000-000000000000'
                    })
                });
                
                const responseText = await response.text();
                
                result.className = response.ok ? 'result success' : 'result error';
                result.textContent = `📡 Edge Function Response (${response.status}):\n${responseText}`;
                
                if (response.status === 200) {
                    result.textContent += '\n\n✅ Edge Function is working correctly!';
                    return true;
                } else if (response.status === 400) {
                    result.textContent += '\n\n⚠️ Edge Function reachable but returned validation error (expected for test data)';
                    return true;
                } else {
                    result.textContent += '\n\n❌ Edge Function returned error status';
                    return false;
                }
                
            } catch (err) {
                result.className = 'result error';
                result.textContent = `❌ Edge Function test failed: ${err.message}`;
                return false;
            } finally {
                button.disabled = false;
            }
        }
        
        async function testDatabaseConfiguration() {
            const button = event.target;
            const result = document.getElementById('dbConfigResult');

            button.disabled = true;
            result.style.display = 'block';
            result.className = 'result info';
            result.textContent = 'Testing database configuration...\n';

            try {
                // Test database settings
                result.textContent += '🔧 Checking database configuration...\n';
                const { data: configData, error: configError } = await supabaseClient
                    .from('pg_settings')
                    .select('name, setting')
                    .in('name', ['app.supabase_url', 'app.service_role_key']);

                if (configError) {
                    result.textContent += `❌ Config check failed: ${configError.message}\n`;
                } else {
                    result.textContent += '📊 Database Configuration:\n';
                    configData.forEach(setting => {
                        if (setting.name === 'app.service_role_key') {
                            result.textContent += `- ${setting.name}: ${setting.setting ? '[SET - ' + setting.setting.length + ' chars]' : '[NOT SET]'}\n`;
                        } else {
                            result.textContent += `- ${setting.name}: ${setting.setting || '[NOT SET]'}\n`;
                        }
                    });
                }

                // Test HTTP extension
                result.textContent += '\n🔌 Checking HTTP extension...\n';
                const { data: extData, error: extError } = await supabaseClient
                    .from('pg_extension')
                    .select('extname')
                    .eq('extname', 'http');

                if (extError) {
                    result.textContent += `❌ Extension check failed: ${extError.message}\n`;
                } else if (extData && extData.length > 0) {
                    result.textContent += '✅ HTTP extension is enabled\n';
                } else {
                    result.textContent += '❌ HTTP extension is NOT enabled\n';
                }

                result.className = 'result success';
                return true;

            } catch (err) {
                result.className = 'result error';
                result.textContent += `❌ Database configuration test failed: ${err.message}`;
                return false;
            } finally {
                button.disabled = false;
            }
        }

        async function testEmailFunctionDirect() {
            const button = event.target;
            const result = document.getElementById('emailFunctionResult');

            if (TEST_EMAIL === '<EMAIL>') {
                result.style.display = 'block';
                result.className = 'result error';
                result.textContent = '⚠️ Please update the test email address first!';
                return false;
            }

            button.disabled = true;
            result.style.display = 'block';
            result.className = 'result info';
            result.textContent = 'Testing email function directly...\n';

            try {
                const testGuestId = '00000000-0000-0000-0000-000000000000';
                const testCode = '123456';

                result.textContent += `📧 Calling send_guest_verification_email_v2...\n`;
                result.textContent += `- Email: ${TEST_EMAIL}\n`;
                result.textContent += `- Code: ${testCode}\n`;
                result.textContent += `- Guest ID: ${testGuestId}\n\n`;

                const { data, error } = await supabaseClient.rpc('send_guest_verification_email_v3', {
                    p_email: TEST_EMAIL,
                    p_verification_code: testCode,
                    p_guest_id: testGuestId
                });

                if (error) {
                    result.className = 'result error';
                    result.textContent += `❌ Email function failed: ${error.message}\n`;
                    result.textContent += `Error details: ${JSON.stringify(error, null, 2)}`;
                    return false;
                }

                result.textContent += '📊 Email Function Results:\n';
                result.textContent += JSON.stringify(data, null, 2) + '\n\n';

                if (data && data.length > 0) {
                    const emailResult = data[0];
                    result.textContent += `📈 Analysis:\n`;
                    result.textContent += `- Success: ${emailResult.success}\n`;
                    result.textContent += `- HTTP Status: ${emailResult.http_status}\n`;
                    result.textContent += `- Error Message: ${emailResult.error_message || 'None'}\n`;
                    result.textContent += `- Response Body: ${emailResult.response_body || 'None'}\n`;

                    if (emailResult.success) {
                        result.className = 'result success';
                        result.textContent += '\n✅ Email function executed successfully!';
                        return true;
                    } else {
                        result.className = 'result error';
                        result.textContent += '\n❌ Email function failed!';
                        return false;
                    }
                } else {
                    result.className = 'result error';
                    result.textContent += '❌ No data returned from email function';
                    return false;
                }

            } catch (err) {
                result.className = 'result error';
                result.textContent += `❌ Email function test failed: ${err.message}\n`;
                result.textContent += `Stack trace: ${err.stack}`;
                return false;
            } finally {
                button.disabled = false;
            }
        }

        async function runAllTests() {
            const button = event.target;
            const result = document.getElementById('allTestsResult');
            
            if (TEST_EMAIL === '<EMAIL>') {
                result.style.display = 'block';
                result.className = 'result error';
                result.textContent = '⚠️ Please update the test email address first!';
                return;
            }
            
            button.disabled = true;
            result.style.display = 'block';
            result.className = 'result info';
            result.textContent = '🚀 Running all tests...\n';
            
            const results = {
                diagnostics: false,
                dbConfig: false,
                emailFunction: false,
                guestCreation: false,
                edgeFunction: false
            };

            // Run all tests
            result.textContent += '\n1. Testing Database Diagnostics...';
            results.diagnostics = await testDatabaseDiagnostics();

            result.textContent += '\n\n2. Testing Database Configuration...';
            results.dbConfig = await testDatabaseConfiguration();

            result.textContent += '\n\n3. Testing Email Function Direct...';
            results.emailFunction = await testEmailFunctionDirect();

            result.textContent += '\n\n4. Testing Guest User Creation...';
            results.guestCreation = await testGuestUserCreation();

            result.textContent += '\n\n5. Testing Edge Function...';
            results.edgeFunction = await testEdgeFunctionDirect();

            // Summary
            result.textContent += '\n\n📋 Test Summary:\n';
            result.textContent += `Database Diagnostics: ${results.diagnostics ? '✅ PASS' : '❌ FAIL'}\n`;
            result.textContent += `Database Configuration: ${results.dbConfig ? '✅ PASS' : '❌ FAIL'}\n`;
            result.textContent += `Email Function Direct: ${results.emailFunction ? '✅ PASS' : '❌ FAIL'}\n`;
            result.textContent += `Guest User Creation: ${results.guestCreation ? '✅ PASS' : '❌ FAIL'}\n`;
            result.textContent += `Edge Function Direct: ${results.edgeFunction ? '✅ PASS' : '❌ FAIL'}\n`;
            
            const allPassed = Object.values(results).every(result => result);
            
            if (allPassed) {
                result.className = 'result success';
                result.textContent += '\n🎉 All tests passed! Email system should be working correctly.';
            } else {
                result.className = 'result error';
                result.textContent += '\n⚠️ Some tests failed. Please check the individual test results above.';
            }
            
            button.disabled = false;
        }
    </script>
</body>
</html>
