import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Alert,
  Paper,
  useTheme,
  CircularProgress,
  Divider
} from '@mui/material';
import { Email as EmailIcon, Send as SendIcon } from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

interface SimplifiedEmailAuthProps {
  onLoginSuccess?: () => void;
  onBackToQuickGuest?: () => void;
}

const SimplifiedEmailAuth: React.FC<SimplifiedEmailAuthProps> = ({ 
  onLoginSuccess, 
  onBackToQuickGuest 
}) => {
  const theme = useTheme();
  const { signInWithMagicLink } = useAuth();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { error: authError } = await signInWithMagicLink(email);
      
      if (authError) {
        setError(authError.message || 'Failed to send magic link');
      } else {
        setSuccess(true);
      }
    } catch (err) {
      setError('An unexpected error occurred');
      console.error('Magic link error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>        <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
          <EmailIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
          <Typography variant="h5" component="h1" gutterBottom>
            Check Your Email
          </Typography>
        </Box>

        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Magic Link Sent! ✨
          </Typography>
          <Typography variant="body2">
            We've sent a secure login link to <strong>{email}</strong>. 
            Click the link in your email to access DataStatPro instantly.
          </Typography>
        </Alert>

        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 3 }}>
          The link will expire in 1 hour for security. Check your spam folder if you don't see it.
        </Typography>

        <Button
          fullWidth
          variant="outlined"
          onClick={() => {
            setSuccess(false);
            setEmail('');
          }}
          sx={{ mb: 2 }}
        >
          Send Another Link
        </Button>

        {onBackToQuickGuest && (
          <>
            <Divider sx={{ my: 2 }}>
              <Typography variant="caption" color="text.secondary">
                or
              </Typography>
            </Divider>
            <Button
              fullWidth
              variant="text"
              onClick={onBackToQuickGuest}
              color="secondary"
            >
              Use Quick Guest Access Instead
            </Button>
          </>
        )}
      </Paper>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <EmailIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Email Access
        </Typography>
        <Typography variant="subtitle2" color="text.secondary" textAlign="center">
          Get a secure login link sent to your email
        </Typography>
      </Box>

      <Alert 
        severity="info" 
        sx={{ 
          mb: 3, 
          border: `1px solid ${theme.palette.info.main}`,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          🔐 Secure Email Authentication
        </Typography>
        <Typography variant="body2" component="ul" sx={{ pl: 2, m: 0 }}>
          <li>✅ No password required</li>
          <li>✅ Secure one-click login</li>
          <li>✅ Full account features</li>
          <li>✅ Data saving & importing</li>
          <li>📧 Works with any email</li>
        </Typography>
      </Alert>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleEmailSubmit}>
        <TextField
          fullWidth
          type="email"
          label="Email Address"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={loading}
          required
          sx={{ mb: 3 }}
          placeholder="<EMAIL>"
        />

        <Button
          fullWidth
          type="submit"
          variant="contained"
          color="primary"
          size="large"
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          disabled={loading || !email.trim()}
          sx={{ 
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 'medium',
            mb: 2
          }}
        >
          {loading ? 'Sending Link...' : 'Send Magic Link'}
        </Button>
      </form>

      {onBackToQuickGuest && (
        <>
          <Divider sx={{ my: 2 }}>
            <Typography variant="caption" color="text.secondary">
              or
            </Typography>
          </Divider>
          <Button
            fullWidth
            variant="outlined"
            onClick={onBackToQuickGuest}
            color="secondary"
            sx={{ py: 1.5 }}
          >
            Use Quick Guest Access Instead
          </Button>
        </>
      )}

      <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 2, color: 'text.secondary' }}>
        Powered by Supabase Auth • No complex setup required
      </Typography>
    </Paper>
  );
};

export default SimplifiedEmailAuth;