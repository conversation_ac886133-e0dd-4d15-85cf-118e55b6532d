/**
 * Email Debug Test Script
 * Run this with Node.js to test the email system
 */

import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

// Test email - replace with your email
const TEST_EMAIL = '<EMAIL>';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testDatabaseDiagnostics() {
    console.log('🔍 Testing Database Diagnostics...');
    console.log('-'.repeat(40));
    
    try {
        const { data, error } = await supabase.rpc('diagnose_email_system');
        
        if (error) {
            console.error('❌ Database diagnostics failed:', error.message);
            return false;
        }
        
        console.log('📊 Diagnostic Results:');
        console.table(data);
        
        // Check for issues
        const issues = data.filter(item => 
            item.status !== 'ENABLED' && 
            item.status !== 'CONFIGURED' && 
            item.status !== 'REACHABLE'
        );
        
        if (issues.length === 0) {
            console.log('✅ All database components are properly configured!');
            return true;
        } else {
            console.log('⚠️ Issues found:');
            issues.forEach(issue => {
                console.log(`- ${issue.component}: ${issue.status} - ${issue.action_required}`);
            });
            return false;
        }
        
    } catch (err) {
        console.error('❌ Diagnostic test failed:', err.message);
        return false;
    }
}

async function testEmailFunctionDirect() {
    console.log('📧 Testing Email Function Direct...');
    console.log('-'.repeat(40));
    
    try {
        const testGuestId = '00000000-0000-0000-0000-000000000000';
        const testCode = '123456';
        
        console.log(`Testing with:`);
        console.log(`- Email: ${TEST_EMAIL}`);
        console.log(`- Code: ${testCode}`);
        console.log(`- Guest ID: ${testGuestId}`);
        
        const { data, error } = await supabase.rpc('send_guest_verification_email_v2', {
            p_email: TEST_EMAIL,
            p_verification_code: testCode,
            p_guest_id: testGuestId
        });
        
        if (error) {
            console.error('❌ Email function failed:', error.message);
            console.error('Error details:', error);
            return false;
        }
        
        console.log('📊 Email Function Results:');
        console.table(data);
        
        if (data && data.length > 0) {
            const emailResult = data[0];
            console.log(`📈 Analysis:`);
            console.log(`- Success: ${emailResult.success}`);
            console.log(`- HTTP Status: ${emailResult.http_status}`);
            console.log(`- Error Message: ${emailResult.error_message || 'None'}`);
            console.log(`- Response Body: ${emailResult.response_body || 'None'}`);
            
            if (emailResult.success) {
                console.log('✅ Email function executed successfully!');
                return true;
            } else {
                console.log('❌ Email function failed!');
                return false;
            }
        } else {
            console.log('❌ No data returned from email function');
            return false;
        }
        
    } catch (err) {
        console.error('❌ Email function test failed:', err.message);
        return false;
    }
}

async function testGuestUserCreation() {
    console.log('👤 Testing Guest User Creation...');
    console.log('-'.repeat(40));
    
    try {
        // Clean up any existing test user
        console.log('🧹 Cleaning up existing test user...');
        try {
            await supabase.from('guest_users').delete().eq('email', TEST_EMAIL);
            console.log('✅ Cleanup completed');
        } catch (cleanupError) {
            console.log(`⚠️ Cleanup warning: ${cleanupError.message}`);
        }
        
        console.log('👤 Creating guest user...');
        const { data, error } = await supabase.rpc('create_guest_user_v2', {
            p_email: TEST_EMAIL,
            p_full_name: 'Test User',
            p_institution: 'Email Test',
            p_marketing_consent: false
        });
        
        if (error) {
            console.error('❌ Guest user creation failed:', error.message);
            console.error('Error details:', error);
            return false;
        }
        
        console.log('📧 Guest User Creation Results:');
        console.table(data);
        
        if (data && data.length > 0) {
            const resultData = data[0];
            console.log(`📊 Analysis:`);
            console.log(`- Guest ID: ${resultData.guest_id}`);
            console.log(`- Verification Token: ${resultData.verification_token}`);
            console.log(`- Success: ${resultData.success}`);
            console.log(`- Email Sent: ${resultData.email_sent}`);
            console.log(`- Message: ${resultData.message}`);
            if (resultData.email_error) {
                console.log(`- Email Error: ${resultData.email_error}`);
            }
            
            if (resultData.success && resultData.email_sent) {
                console.log(`✅ Guest user created and email sent successfully!`);
                console.log(`📬 Check ${TEST_EMAIL} for the verification code: ${resultData.verification_token}`);
                return true;
            } else {
                console.log(`⚠️ Guest user created but email failed!`);
                return false;
            }
        } else {
            console.log('❌ No data returned from guest user creation');
            return false;
        }
        
    } catch (err) {
        console.error('❌ Guest user creation test failed:', err.message);
        return false;
    }
}

async function testEdgeFunctionDirect() {
    console.log('🔗 Testing Edge Function Direct...');
    console.log('-'.repeat(40));
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/send-guest-verification-email`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: TEST_EMAIL,
                verificationCode: '123456',
                guestId: '00000000-0000-0000-0000-000000000000'
            })
        });
        
        const responseText = await response.text();
        
        console.log(`📡 Edge Function Response (${response.status}):`);
        console.log(responseText);
        
        if (response.status === 200) {
            console.log('✅ Edge Function is working correctly!');
            return true;
        } else if (response.status === 400) {
            console.log('⚠️ Edge Function reachable but returned validation error (expected for test data)');
            return true;
        } else {
            console.log('❌ Edge Function returned error status');
            return false;
        }
        
    } catch (err) {
        console.error('❌ Edge Function test failed:', err.message);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 Starting Email System Debug Tests...');
    console.log('='.repeat(50));
    
    if (TEST_EMAIL === '<EMAIL>') {
        console.log('⚠️ Please update TEST_EMAIL in the script with your actual email address');
        return;
    }
    
    const results = {
        diagnostics: false,
        emailFunction: false,
        guestCreation: false,
        edgeFunction: false
    };
    
    // Test 1: Database Diagnostics
    results.diagnostics = await testDatabaseDiagnostics();
    console.log('');
    
    // Test 2: Email Function Direct
    results.emailFunction = await testEmailFunctionDirect();
    console.log('');
    
    // Test 3: Guest User Creation
    results.guestCreation = await testGuestUserCreation();
    console.log('');
    
    // Test 4: Edge Function Direct Test
    results.edgeFunction = await testEdgeFunctionDirect();
    console.log('');
    
    // Summary
    console.log('📋 Test Summary:');
    console.log('='.repeat(50));
    console.log(`Database Diagnostics: ${results.diagnostics ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Email Function Direct: ${results.emailFunction ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Guest User Creation: ${results.guestCreation ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Edge Function Direct: ${results.edgeFunction ? '✅ PASS' : '❌ FAIL'}`);
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('🎉 All tests passed! Email system should be working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Please check the issues above and apply the necessary fixes.');
    }
    
    return results;
}

// Run the tests
runAllTests().catch(console.error);
