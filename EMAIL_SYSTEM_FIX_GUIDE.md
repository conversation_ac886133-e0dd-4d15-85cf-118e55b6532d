# Email System Fix Guide

Based on the test results, here are the issues found and their solutions:

## 🚨 Critical Issues Found

### 1. Domain Verification Issue (CRITICAL)
**Error**: `The datastatpro.com domain is not verified. Please, add and verify your domain on https://resend.com/domains`

**Solution**:
1. Go to [Resend Domains](https://resend.com/domains)
2. Add your domain: `datastatpro.com`
3. Follow the DNS verification steps
4. Wait for verification to complete

**Alternative**: Use a verified email address like `<EMAIL>` temporarily for testing

### 2. Database Configuration Missing
**Error**: `unrecognized configuration parameter "app.supabase_url"`

**Solution**: Apply the database configuration using the SQL commands below.

### 3. Missing Verification Function
**Error**: `Could not find the function public.verify_guest_email`

**Solution**: The function exists but with different parameters. Need to update the test.

## 🔧 Quick Fixes

### Fix 1: Apply Database Configuration

Run these SQL commands in your Supabase SQL Editor:

```sql
-- Set database configuration parameters
ALTER DATABASE postgres SET app.supabase_url = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
ALTER DATABASE postgres SET app.service_role_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjUzMzkwNSwiZXhwIjoyMDYyMTA5OTA1fQ.P4ro4H7Tn1Nx7NxeRZDV37ptWVk13aokfgggTcVSqOI';

-- Reload configuration
SELECT pg_reload_conf();
```

### Fix 2: Update FROM_EMAIL in Edge Function

Temporarily change the `FROM_EMAIL` environment variable in Supabase Dashboard:
- Go to Edge Functions → send-guest-verification-email → Settings
- Change `FROM_EMAIL` from `<EMAIL>` to `<EMAIL>`
- This will work immediately for testing

### Fix 3: Verify Domain in Resend (Permanent Solution)

1. **Login to Resend Dashboard**: https://resend.com/domains
2. **Add Domain**: Click "Add Domain" and enter `datastatpro.com`
3. **DNS Configuration**: Add the provided DNS records to your domain:
   - TXT record for domain verification
   - MX records for email receiving (optional)
   - DKIM records for authentication
4. **Wait for Verification**: This can take up to 72 hours
5. **Update FROM_EMAIL**: Once verified, change back to `<EMAIL>`

## 🧪 Test Results Summary

✅ **WORKING**:
- Database function creates guest users correctly
- AuthContext integration is properly structured
- Email sending function calls Edge Function

❌ **NEEDS FIXING**:
- Domain verification in Resend
- Database configuration parameters
- Email verification function parameters

## 🚀 Quick Test After Fixes

After applying the fixes, run:
```bash
node test-email-verification-system.js
```

Expected results:
- ✅ All diagnostics should pass
- ✅ Edge Function should return 200 status
- ✅ Email should be sent successfully
- ✅ Verification flow should complete

## 📧 Alternative: Use Magic Links

If domain verification is taking too long, you can switch to Supabase's built-in magic link authentication:

1. **Enable Magic Links** in Supabase Dashboard → Authentication → Settings
2. **Update AuthContext** to use `supabase.auth.signInWithOtp()`
3. **No domain verification required** - uses Supabase's email service

## 🔍 Monitoring

After fixes:
1. **Check Edge Function logs** in Supabase Dashboard
2. **Monitor email delivery** in Resend Dashboard
3. **Test with real email addresses** to verify delivery

---

**Next Steps**: Apply Fix 1 and Fix 2 immediately, then work on domain verification for the permanent solution.