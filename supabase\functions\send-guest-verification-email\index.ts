import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailRequest {
  email: string
  verificationCode: string
  guestId: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')!
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )

    // Parse the request body
    const { email, verificationCode, guestId }: EmailRequest = await req.json()

    if (!email || !verificationCode || !guestId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: email, verificationCode, guestId' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get Resend API key from environment
    const resendApiKey = Deno.env.get('RESEND_API_KEY')
    if (!resendApiKey) {
      console.error('RESEND_API_KEY not found in environment variables')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Prepare email content
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>DataStatPro - Email Verification</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
          }
          .container {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
          }
          .verification-code {
            background: #f5f5f5;
            border: 2px dashed #1976d2;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 30px 0;
          }
          .code {
            font-size: 32px;
            font-weight: bold;
            color: #1976d2;
            letter-spacing: 4px;
            font-family: 'Courier New', monospace;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
            text-align: center;
          }
          .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">DataStatPro</div>
            <h1>Email Verification</h1>
            <p>Welcome to DataStatPro! Please verify your email address to access guest features.</p>
          </div>
          
          <div class="verification-code">
            <p><strong>Your verification code is:</strong></p>
            <div class="code">${verificationCode}</div>
          </div>
          
          <div class="warning">
            <strong>Important:</strong> This verification code will expire in 24 hours. Please use it soon to complete your registration.
          </div>
          
          <p>Enter this code in the DataStatPro application to verify your email address and start exploring our statistical analysis tools with sample datasets.</p>
          
          <p>As a guest user, you'll have access to:</p>
          <ul>
            <li>Sample datasets for analysis</li>
            <li>Basic statistical tools</li>
            <li>Data visualization features</li>
          </ul>
          
          <div class="footer">
            <p>If you didn't request this verification code, you can safely ignore this email.</p>
            <p>This is an automated message from DataStatPro. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `

    const emailText = `
DataStatPro - Email Verification

Welcome to DataStatPro! Please verify your email address to access guest features.

Your verification code is: ${verificationCode}

This verification code will expire in 24 hours. Please use it soon to complete your registration.

Enter this code in the DataStatPro application to verify your email address and start exploring our statistical analysis tools with sample datasets.

As a guest user, you'll have access to:
- Sample datasets for analysis
- Basic statistical tools  
- Data visualization features

If you didn't request this verification code, you can safely ignore this email.

This is an automated message from DataStatPro. Please do not reply to this email.
    `

    // Send email using Resend API
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'DataStatPro <<EMAIL>>',
        to: [email],
        subject: 'DataStatPro - Verify Your Email Address',
        html: emailHtml,
        text: emailText,
      }),
    })

    if (!emailResponse.ok) {
      const errorData = await emailResponse.text()
      console.error('Resend API error:', errorData)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to send verification email',
          details: errorData 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const emailResult = await emailResponse.json()
    console.log('Email sent successfully:', emailResult)

    // Log the email sending event (optional)
    try {
      await supabaseClient
        .from('guest_users')
        .update({ 
          updated_at: new Date().toISOString(),
          // You could add an email_sent_at field if needed
        })
        .eq('id', guestId)
    } catch (logError) {
      console.warn('Failed to log email sending event:', logError)
      // Don't fail the request if logging fails
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Verification email sent successfully',
        emailId: emailResult.id 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in send-guest-verification-email function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})