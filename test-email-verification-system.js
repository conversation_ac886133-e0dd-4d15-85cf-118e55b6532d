/**
 * Comprehensive Email Verification System Test
 * This script tests the complete email verification flow
 */

import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.OCeU38SWqhgeprIekN8qoaPdCzu04RaU-ktdNOci4rs';
const SUPABASE_SERVICE_ROLE = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjUzMzkwNSwiZXhwIjoyMDYyMTA5OTA1fQ.P4ro4H7Tn1Nx7NxeRZDV37ptWVk13aokfgggTcVSqOI';

// Test email (use your own email for testing)
const TEST_EMAIL = '<EMAIL>';

/**
 * Test 1: System Diagnostics
 * Check if all components are properly configured
 */
async function testSystemDiagnostics() {
    console.log('🔍 Running System Diagnostics...');
    console.log('-'.repeat(40));
    
    try {
        const client = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE);
        
        const { data, error } = await client.rpc('diagnose_email_system');
        
        if (error) {
            console.error('❌ Diagnostic function failed:', error.message);
            return false;
        }
        
        if (!data || data.length === 0) {
            console.log('⚠️  No diagnostic data returned');
            return false;
        }
        
        let allGood = true;
        data.forEach(item => {
            const status = item.status === 'ENABLED' || item.status === 'CONFIGURED' || item.status === 'REACHABLE' ? '✅' : '❌';
            console.log(`${status} ${item.component}: ${item.status}`);
            console.log(`   Details: ${item.details}`);
            if (item.action_required !== 'None') {
                console.log(`   Action: ${item.action_required}`);
                allGood = false;
            }
            console.log('');
        });
        
        return allGood;
        
    } catch (error) {
        console.error('❌ System diagnostics failed:', error.message);
        return false;
    }
}

/**
 * Test 2: Database Function Test
 * Test the improved create_guest_user_v2 function
 */
async function testDatabaseFunction() {
    console.log('🗄️  Testing Database Function...');
    console.log('-'.repeat(40));
    
    try {
        const client = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Clean up any existing test user first
        await client.from('guest_users').delete().eq('email', TEST_EMAIL);
        
        const { data, error } = await client.rpc('create_guest_user_v2', {
            p_email: TEST_EMAIL,
            p_full_name: 'Test User',
            p_institution: 'DataStatPro Testing',
            p_marketing_consent: false
        });
        
        if (error) {
            console.error('❌ Database function failed:', error.message);
            return null;
        }
        
        if (!data || data.length === 0) {
            console.log('⚠️  No data returned from database function');
            return null;
        }
        
        const result = data[0];
        console.log('✅ Database function executed successfully');
        console.log(`   Guest ID: ${result.guest_id}`);
        console.log(`   Verification Token: ${result.verification_token}`);
        console.log(`   Success: ${result.success}`);
        console.log(`   Message: ${result.message}`);
        console.log(`   Email Sent: ${result.email_sent}`);
        if (result.email_error) {
            console.log(`   Email Error: ${result.email_error}`);
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ Database function test failed:', error.message);
        return null;
    }
}

/**
 * Test 3: Edge Function Direct Test
 * Test the Edge Function directly
 */
async function testEdgeFunctionDirect() {
    console.log('🔧 Testing Edge Function Directly...');
    console.log('-'.repeat(40));
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/send-guest-verification-email`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: TEST_EMAIL,
                verificationCode: '123456',
                guestId: '00000000-0000-0000-0000-000000000000'
            })
        });
        
        const responseText = await response.text();
        
        console.log(`   Status: ${response.status}`);
        console.log(`   Response: ${responseText}`);
        
        if (response.ok) {
            console.log('✅ Edge Function is working correctly');
            return true;
        } else {
            console.log('❌ Edge Function returned an error');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Edge Function test failed:', error.message);
        return false;
    }
}

/**
 * Test 4: AuthContext Integration Test
 * Test how AuthContext handles the responses
 */
async function testAuthContextIntegration() {
    console.log('🔐 Testing AuthContext Integration...');
    console.log('-'.repeat(40));
    
    try {
        // This simulates what AuthContext does
        const client = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        const { data, error } = await client.rpc('create_guest_user_v2', {
            p_email: `test-${Date.now()}@datastatpro.com`,
            p_full_name: 'AuthContext Test User',
            p_marketing_consent: false
        });
        
        if (error) {
            console.error('❌ AuthContext integration failed:', error.message);
            return false;
        }
        
        const result = data[0];
        
        // Check if the response structure matches what AuthContext expects
        const expectedFields = ['guest_id', 'verification_token', 'success', 'message', 'email_sent'];
        const missingFields = expectedFields.filter(field => !(field in result));
        
        if (missingFields.length > 0) {
            console.log(`⚠️  Missing expected fields: ${missingFields.join(', ')}`);
            return false;
        }
        
        console.log('✅ AuthContext integration structure is correct');
        console.log(`   All expected fields present: ${expectedFields.join(', ')}`);
        
        return true;
        
    } catch (error) {
        console.error('❌ AuthContext integration test failed:', error.message);
        return false;
    }
}

/**
 * Test 5: Email Verification Flow
 * Test the complete verification flow
 */
async function testEmailVerificationFlow() {
    console.log('📧 Testing Email Verification Flow...');
    console.log('-'.repeat(40));
    
    try {
        const client = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Step 1: Create guest user
        const testEmail = `verification-test-${Date.now()}@datastatpro.com`;
        const { data: createData, error: createError } = await client.rpc('create_guest_user_v2', {
            p_email: testEmail,
            p_full_name: 'Verification Test User'
        });
        
        if (createError || !createData || createData.length === 0) {
            console.error('❌ Failed to create guest user for verification test');
            return false;
        }
        
        const guestData = createData[0];
        console.log(`✅ Guest user created: ${guestData.guest_id}`);
        console.log(`   Verification token: ${guestData.verification_token}`);
        
        // Step 2: Verify the email using the token
        const { data: verifyData, error: verifyError } = await client.rpc('verify_guest_email', {
            p_token: guestData.verification_token
        });
        
        if (verifyError) {
            console.error('❌ Email verification failed:', verifyError.message);
            return false;
        }
        
        if (!verifyData || verifyData.length === 0) {
            console.log('⚠️  No verification data returned');
            return false;
        }
        
        const verifyResult = verifyData[0];
        console.log('✅ Email verification completed');
        console.log(`   Verified guest ID: ${verifyResult.guest_id}`);
        console.log(`   Verified email: ${verifyResult.email}`);
        console.log(`   Success: ${verifyResult.success}`);
        
        return verifyResult.success;
        
    } catch (error) {
        console.error('❌ Email verification flow test failed:', error.message);
        return false;
    }
}

/**
 * Run All Tests
 * Execute all tests in sequence
 */
async function runAllTests() {
    console.log('🚀 Starting Comprehensive Email System Tests...');
    console.log('='.repeat(60));
    console.log('');
    
    const results = {
        diagnostics: false,
        databaseFunction: false,
        edgeFunction: false,
        authContext: false,
        verificationFlow: false
    };
    
    // Test 1: System Diagnostics
    results.diagnostics = await testSystemDiagnostics();
    console.log('');
    
    // Test 2: Database Function
    const dbResult = await testDatabaseFunction();
    results.databaseFunction = dbResult !== null;
    console.log('');
    
    // Test 3: Edge Function Direct
    results.edgeFunction = await testEdgeFunctionDirect();
    console.log('');
    
    // Test 4: AuthContext Integration
    results.authContext = await testAuthContextIntegration();
    console.log('');
    
    // Test 5: Email Verification Flow
    results.verificationFlow = await testEmailVerificationFlow();
    console.log('');
    
    // Summary
    console.log('📊 Test Results Summary');
    console.log('='.repeat(60));
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASSED' : '❌ FAILED';
        console.log(`${status} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
    });
    
    const totalPassed = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log('');
    console.log(`Overall: ${totalPassed}/${totalTests} tests passed`);
    
    if (totalPassed === totalTests) {
        console.log('🎉 All tests passed! Email system is working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please check the issues above.');
    }
    
    return results;
}

// Run tests if this script is executed directly
runAllTests().catch(console.error);