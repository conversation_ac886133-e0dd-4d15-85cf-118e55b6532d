-- Configure Email System Database Parameters
-- This migration sets up the required database configuration for email sending

-- Set database configuration parameters for email system
ALTER DATABASE postgres SET app.supabase_url = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
ALTER DATABASE postgres SET app.service_role_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjUzMzkwNSwiZXhwIjoyMDYyMTA5OTA1fQ.P4ro4H7Tn1Nx7NxeRZDV37ptWVk13aokfgggTcVSqOI';

-- Reload configuration to apply changes
SELECT pg_reload_conf();

-- Test the configuration
DO $$
DECLARE
    v_supabase_url TEXT;
    v_service_role_key TEXT;
BEGIN
    -- Test if configuration is accessible
    BEGIN
        v_supabase_url := current_setting('app.supabase_url');
        v_service_role_key := current_setting('app.service_role_key');
        
        RAISE NOTICE 'Email system configuration applied successfully';
        RAISE NOTICE 'Supabase URL: %', v_supabase_url;
        RAISE NOTICE 'Service Role Key length: % characters', length(v_service_role_key);
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Failed to read configuration: %', SQLERRM;
    END;
END
$$;

-- Add comment for documentation
COMMENT ON DATABASE postgres IS 'Email system configuration applied for guest verification emails';