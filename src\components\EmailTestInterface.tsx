import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Alert, Paper, CircularProgress } from '@mui/material';
import { useAuth } from '../context/AuthContext';

interface EmailTestInterfaceProps {
  onClose?: () => void;
}

export const EmailTestInterface: React.FC<EmailTestInterfaceProps> = ({ onClose }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { createGuestUser } = useAuth();

  const handleTestEmail = async () => {
    if (!email) {
      setError('Please enter an email address');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await createGuestUser({
        email,
        fullName: 'Test User',
        institution: 'Email Test',
        marketingConsent: false
      });

      setResult(response);
      
      if (response.success) {
        setError(null);
      } else {
        setError(response.message || 'Failed to send email');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleDirectEdgeFunctionTest = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('https://ghzibvkqmdlpyaidfbah.supabase.co/functions/v1/send-guest-verification-email', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.OCeU38SWqhgeprIekN8qoaPdCzu04RaU-ktdNOci4rs',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: email || '<EMAIL>',
          verificationCode: '123456',
          guestId: '00000000-0000-0000-0000-000000000000'
        })
      });

      const responseText = await response.text();
      
      setResult({
        status: response.status,
        statusText: response.statusText,
        response: responseText,
        success: response.ok
      });

      if (!response.ok) {
        setError(`Edge Function Error: ${response.status} - ${responseText}`);
      }
    } catch (err: any) {
      setError(`Network Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 600, mx: 'auto', mt: 2 }}>
      <Typography variant="h5" gutterBottom>
        📧 Email System Test Interface
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Test the email verification system to ensure it's working correctly.
      </Typography>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label="Test Email Address"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email to test"
          disabled={loading}
        />
      </Box>

      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button
          variant="contained"
          onClick={handleTestEmail}
          disabled={loading || !email}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          Test Complete Flow
        </Button>
        
        <Button
          variant="outlined"
          onClick={handleDirectEdgeFunctionTest}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          Test Edge Function Only
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Error:</strong> {error}
          </Typography>
        </Alert>
      )}

      {result && (
        <Alert 
          severity={result.success ? 'success' : 'warning'} 
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            <strong>Result:</strong>
          </Typography>
          <Box component="pre" sx={{ 
            fontSize: '0.75rem', 
            mt: 1, 
            p: 1, 
            bgcolor: 'rgba(0,0,0,0.1)', 
            borderRadius: 1,
            overflow: 'auto',
            maxHeight: 200
          }}>
            {JSON.stringify(result, null, 2)}
          </Box>
        </Alert>
      )}

      <Alert severity="info" sx={{ mt: 2 }}>
        <Typography variant="body2">
          <strong>Common Issues:</strong>
        </Typography>
        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
          <li>Domain not verified in Resend (403 error)</li>
          <li>Missing RESEND_API_KEY environment variable</li>
          <li>Database configuration not set</li>
          <li>Edge Function not deployed</li>
        </ul>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Check the <strong>EMAIL_SYSTEM_FIX_GUIDE.md</strong> for solutions.
        </Typography>
      </Alert>

      {onClose && (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button onClick={onClose}>
            Close
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default EmailTestInterface;