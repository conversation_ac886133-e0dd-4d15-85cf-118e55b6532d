import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  Paper,
  useTheme,
  CircularProgress
} from '@mui/material';
import { PersonOutline as PersonOutlineIcon, Speed as SpeedIcon } from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

interface SimpleGuestAccessProps {
  onLoginSuccess?: () => void;
}

const SimpleGuestAccess: React.FC<SimpleGuestAccessProps> = ({ onLoginSuccess }) => {
  const theme = useTheme();
  const { loginAsGuestDirect } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleGuestLogin = async () => {
    setLoading(true);
    try {
      loginAsGuestDirect();
      if (onLoginSuccess) {
        onLoginSuccess();
      }
    } catch (error) {
      console.error('Error during guest login:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <SpeedIcon fontSize="large" color="secondary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Quick Guest Access
        </Typography>
        <Typography variant="subtitle2" color="text.secondary" textAlign="center">
          Start exploring immediately - no setup required
        </Typography>
      </Box>

      <Alert 
        severity="success" 
        sx={{ 
          mb: 3, 
          border: `1px solid ${theme.palette.success.main}`,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          ✨ Instant Access Features
        </Typography>
        <Typography variant="body2" component="ul" sx={{ pl: 2, m: 0 }}>
          <li>✅ All statistical analysis tools</li>
          <li>✅ Sample datasets for learning</li>
          <li>✅ Advanced analytics features</li>
          <li>✅ Publication-ready outputs</li>
          <li>⚡ No email verification needed</li>
        </Typography>
      </Alert>

      <Typography variant="body2" paragraph textAlign="center" sx={{ mb: 3, color: 'text.secondary' }}>
        Perfect for quick analysis, learning, or evaluating DataStatPro's capabilities.
      </Typography>

      <Button
        fullWidth
        variant="contained"
        color="secondary"
        size="large"
        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <PersonOutlineIcon />}
        onClick={handleGuestLogin}
        disabled={loading}
        sx={{ 
          py: 1.5,
          fontSize: '1.1rem',
          fontWeight: 'medium',
          background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
          '&:hover': {
            background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
          }
        }}
      >
        {loading ? 'Starting Session...' : 'Start Guest Session'}
      </Button>

      <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 2, color: 'text.secondary' }}>
        No registration • No email • No waiting
      </Typography>
    </Paper>
  );
};

export default SimpleGuestAccess;