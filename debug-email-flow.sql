-- Debug Email Flow
-- This script helps debug the complete email flow step by step

-- Step 1: Check if all required functions exist
SELECT 'Checking function existence...' as step;

SELECT 
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'diagnose_email_system',
    'send_guest_verification_email_v2',
    'create_guest_user_v2'
)
ORDER BY routine_name;

-- Step 2: Check function parameters
SELECT 'Checking function parameters...' as step;

SELECT 
    routine_name,
    parameter_name,
    data_type,
    parameter_default
FROM information_schema.parameters 
WHERE specific_schema = 'public' 
AND routine_name IN (
    'send_guest_verification_email_v2',
    'create_guest_user_v2'
)
ORDER BY routine_name, ordinal_position;

-- Step 3: Test database configuration
SELECT 'Testing database configuration...' as step;

DO $$
DECLARE
    v_supabase_url TEXT;
    v_service_role_key TEXT;
    v_http_extension_exists BOOLEAN;
BEGIN
    -- Check HTTP extension
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'http'
    ) INTO v_http_extension_exists;
    
    RAISE NOTICE 'HTTP Extension: %', CASE WHEN v_http_extension_exists THEN 'ENABLED' ELSE 'DISABLED' END;
    
    -- Check configuration
    BEGIN
        v_supabase_url := current_setting('app.supabase_url');
        RAISE NOTICE 'Supabase URL: %', v_supabase_url;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Supabase URL: NOT SET - %', SQLERRM;
    END;
    
    BEGIN
        v_service_role_key := current_setting('app.service_role_key');
        RAISE NOTICE 'Service Role Key: SET (% chars)', length(v_service_role_key);
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'Service Role Key: NOT SET - %', SQLERRM;
    END;
END
$$;

-- Step 4: Test the diagnostic function
SELECT 'Running diagnostic function...' as step;
SELECT * FROM public.diagnose_email_system();

-- Step 5: Create a test function to debug the email sending process
CREATE OR REPLACE FUNCTION public.debug_email_sending(
    p_test_email VARCHAR(255)
) RETURNS TABLE(
    step_name TEXT,
    success BOOLEAN,
    details TEXT,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_id UUID;
    v_token VARCHAR(255);
    v_email_result RECORD;
    v_supabase_url TEXT;
    v_service_role_key TEXT;
BEGIN
    -- Step 1: Check configuration
    BEGIN
        v_supabase_url := current_setting('app.supabase_url');
        v_service_role_key := current_setting('app.service_role_key');
        
        RETURN QUERY SELECT 
            'Configuration Check'::TEXT,
            TRUE,
            'URL: ' || v_supabase_url || ', Key length: ' || length(v_service_role_key)::TEXT,
            NULL::TEXT;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                'Configuration Check'::TEXT,
                FALSE,
                'Failed to read configuration'::TEXT,
                SQLERRM::TEXT;
            RETURN;
    END;
    
    -- Step 2: Generate test data
    v_guest_id := gen_random_uuid();
    v_token := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
    
    RETURN QUERY SELECT 
        'Test Data Generation'::TEXT,
        TRUE,
        'Guest ID: ' || v_guest_id::TEXT || ', Token: ' || v_token::TEXT,
        NULL::TEXT;
    
    -- Step 3: Test email function directly
    BEGIN
        SELECT * INTO v_email_result 
        FROM public.send_guest_verification_email_v2(p_test_email, v_token, v_guest_id);
        
        RETURN QUERY SELECT 
            'Email Function Test'::TEXT,
            v_email_result.success,
            'HTTP Status: ' || COALESCE(v_email_result.http_status::TEXT, 'NULL') || 
            ', Response: ' || COALESCE(v_email_result.response_body, 'NULL'),
            v_email_result.error_message;
            
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                'Email Function Test'::TEXT,
                FALSE,
                'Exception occurred'::TEXT,
                SQLERRM::TEXT;
    END;
    
    -- Step 4: Test complete guest creation flow
    BEGIN
        -- Clean up first
        DELETE FROM public.guest_users WHERE email = p_test_email;
        
        -- Test creation
        SELECT * INTO v_email_result 
        FROM (
            SELECT 
                guest_id,
                verification_token,
                success,
                message,
                email_sent,
                email_error
            FROM public.create_guest_user_v2(
                p_test_email,
                'Debug Test User',
                'Email Debug',
                false
            )
        ) AS result;
        
        RETURN QUERY SELECT 
            'Guest Creation Test'::TEXT,
            v_email_result.success::BOOLEAN,
            'Email sent: ' || COALESCE(v_email_result.email_sent::TEXT, 'NULL') || 
            ', Message: ' || COALESCE(v_email_result.message, 'NULL'),
            v_email_result.email_error;
            
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                'Guest Creation Test'::TEXT,
                FALSE,
                'Exception occurred'::TEXT,
                SQLERRM::TEXT;
    END;
END;
$$;

-- Step 6: Instructions for manual testing
SELECT 'To test the email system manually, run:' as instructions;
SELECT 'SELECT * FROM public.debug_email_sending(''<EMAIL>'');' as command;

-- Uncomment the line below and replace with your email to run the debug test
-- SELECT * FROM public.debug_email_sending('<EMAIL>');
