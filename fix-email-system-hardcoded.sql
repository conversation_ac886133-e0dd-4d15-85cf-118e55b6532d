-- Fix Email System with Hardcoded Configuration
-- This approach works around Supabase's database parameter restrictions

-- Step 1: Enable HTTP extension (this should work)
CREATE EXTENSION IF NOT EXISTS http;

-- Step 2: Create improved email sending function with hardcoded configuration
CREATE OR REPLACE FUNCTION public.send_guest_verification_email_v3(
    p_email VARCHAR(255),
    p_verification_code VARCHAR(255),
    p_guest_id UUID
) RETURNS TABLE(
    success BOOLEAN,
    error_message TEXT,
    http_status INTEGER,
    response_body TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_response TEXT;
    v_status_code INTEGER;
    v_supabase_url TEXT := 'https://ghzibvkqmdlpyaidfbah.supabase.co';
    v_service_role_key TEXT := 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjUzMzkwNSwiZXhwIjoyMDYyMTA5OTA1fQ.P4ro4H7Tn1Nx7NxeRZDV37ptWVk13aokfgggTcVSqOI';
BEGIN
    -- Validate inputs
    IF p_email IS NULL OR p_verification_code IS NULL OR p_guest_id IS NULL THEN
        RETURN QUERY SELECT 
            FALSE,
            'Missing required parameters',
            0,
            NULL::TEXT;
        RETURN;
    END IF;
    
    -- Call the Edge Function to send email
    BEGIN
        SELECT 
            content::TEXT,
            status_code
        INTO 
            v_response,
            v_status_code
        FROM 
            http((
                'POST',
                v_supabase_url || '/functions/v1/send-guest-verification-email',
                ARRAY[
                    http_header('Authorization', 'Bearer ' || v_service_role_key),
                    http_header('Content-Type', 'application/json')
                ],
                'application/json',
                json_build_object(
                    'email', p_email,
                    'verificationCode', p_verification_code,
                    'guestId', p_guest_id
                )::TEXT
            ));
        
        -- Return detailed response
        RETURN QUERY SELECT 
            (v_status_code = 200),
            CASE 
                WHEN v_status_code = 200 THEN 'Email sent successfully'
                ELSE 'Email sending failed: HTTP ' || v_status_code::TEXT
            END,
            v_status_code,
            v_response;
            
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                FALSE,
                'HTTP request failed: ' || SQLERRM,
                0,
                NULL::TEXT;
    END;
END;
$$;

-- Step 3: Update create_guest_user function to use the new email function
CREATE OR REPLACE FUNCTION public.create_guest_user_v3(
    p_email VARCHAR(255),
    p_full_name VARCHAR(255) DEFAULT NULL,
    p_institution VARCHAR(255) DEFAULT NULL,
    p_marketing_consent BOOLEAN DEFAULT FALSE
) RETURNS TABLE(
    guest_id UUID,
    verification_token VARCHAR(255),
    success BOOLEAN,
    message TEXT,
    email_sent BOOLEAN,
    email_error TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_id UUID;
    v_token VARCHAR(255);
    v_existing_user UUID;
    v_email_result RECORD;
BEGIN
    -- Validate email
    IF p_email IS NULL OR p_email = '' THEN
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            'Email address is required'::TEXT,
            FALSE,
            'Email address is required'::TEXT;
        RETURN;
    END IF;
    
    -- Check if email already exists and is verified
    SELECT id INTO v_existing_user 
    FROM public.guest_users 
    WHERE email = p_email AND email_verified = TRUE;
    
    IF v_existing_user IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_existing_user,
            NULL::VARCHAR(255),
            FALSE,
            'Email already verified. Please login instead.'::TEXT,
            FALSE,
            'Email already verified'::TEXT;
        RETURN;
    END IF;
    
    -- Delete any existing unverified users with this email
    DELETE FROM public.guest_users 
    WHERE email = p_email AND email_verified = FALSE;
    
    -- Generate verification token (6-digit code for user-friendly input)
    v_token := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
    
    -- Insert guest user
    INSERT INTO public.guest_users (
        email, 
        verification_token, 
        verification_expires_at,
        marketing_consent,
        full_name,
        institution,
        email_verified,
        created_at,
        updated_at
    ) VALUES (
        p_email, 
        v_token, 
        NOW() + INTERVAL '24 hours',
        p_marketing_consent,
        p_full_name,
        p_institution,
        FALSE,
        NOW(),
        NOW()
    ) RETURNING id INTO v_guest_id;
    
    -- Try to send verification email using improved function
    SELECT * INTO v_email_result 
    FROM public.send_guest_verification_email_v3(p_email, v_token, v_guest_id);
    
    RETURN QUERY SELECT 
        v_guest_id,
        v_token,
        TRUE,
        CASE 
            WHEN v_email_result.success THEN 'Guest user created and verification email sent successfully'
            ELSE 'Guest user created but email sending failed: ' || COALESCE(v_email_result.error_message, 'Unknown error')
        END::TEXT,
        v_email_result.success,
        v_email_result.error_message;
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            'Failed to create guest user: ' || SQLERRM::TEXT,
            FALSE,
            SQLERRM::TEXT;
END;
$$;

-- Step 4: Create a diagnostic function that works without database parameters
CREATE OR REPLACE FUNCTION public.diagnose_email_system_v2()
RETURNS TABLE(
    component TEXT,
    status TEXT,
    details TEXT,
    action_required TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_http_extension_exists BOOLEAN;
    v_test_response TEXT;
    v_test_status INTEGER;
    v_supabase_url TEXT := 'https://ghzibvkqmdlpyaidfbah.supabase.co';
    v_service_role_key TEXT := 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjUzMzkwNSwiZXhwIjoyMDYyMTA5OTA1fQ.P4ro4H7Tn1Nx7NxeRZDV37ptWVk13aokfgggTcVSqOI';
BEGIN
    -- Check if http extension is enabled
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'http'
    ) INTO v_http_extension_exists;
    
    IF v_http_extension_exists THEN
        RETURN QUERY SELECT 
            'HTTP Extension'::TEXT,
            'ENABLED'::TEXT,
            'PostgreSQL http extension is installed and enabled'::TEXT,
            'None'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            'HTTP Extension'::TEXT,
            'MISSING'::TEXT,
            'PostgreSQL http extension is not enabled'::TEXT,
            'Run: CREATE EXTENSION IF NOT EXISTS http;'::TEXT;
        RETURN;
    END IF;
    
    -- Check hardcoded configuration
    RETURN QUERY SELECT 
        'Configuration'::TEXT,
        'HARDCODED'::TEXT,
        'Using hardcoded Supabase URL and service role key'::TEXT,
        'None'::TEXT;
    
    -- Test Edge Function connectivity
    BEGIN
        SELECT 
            content::TEXT,
            status_code
        INTO 
            v_test_response,
            v_test_status
        FROM 
            http((
                'POST',
                v_supabase_url || '/functions/v1/send-guest-verification-email',
                ARRAY[
                    http_header('Authorization', 'Bearer ' || v_service_role_key),
                    http_header('Content-Type', 'application/json')
                ],
                'application/json',
                json_build_object(
                    'email', '<EMAIL>',
                    'verificationCode', '123456',
                    'guestId', gen_random_uuid()
                )::TEXT
            ));
            
        IF v_test_status = 200 THEN
            RETURN QUERY SELECT 
                'Edge Function'::TEXT,
                'REACHABLE'::TEXT,
                'Edge Function responded successfully'::TEXT,
                'None'::TEXT;
        ELSE
            RETURN QUERY SELECT 
                'Edge Function'::TEXT,
                'ERROR'::TEXT,
                'Edge Function returned HTTP ' || v_test_status::TEXT || ': ' || COALESCE(v_test_response, 'No response')::TEXT,
                'Check Edge Function deployment and environment variables'::TEXT;
        END IF;
            
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                'Edge Function'::TEXT,
                'UNREACHABLE'::TEXT,
                'Failed to connect to Edge Function: ' || SQLERRM::TEXT,
                'Check Edge Function deployment and network connectivity'::TEXT;
    END;
END;
$$;

-- Step 5: Add helpful comments
COMMENT ON FUNCTION public.send_guest_verification_email_v3(VARCHAR, VARCHAR, UUID) IS 'Email sending function with hardcoded configuration for Supabase compatibility';
COMMENT ON FUNCTION public.create_guest_user_v3(VARCHAR, VARCHAR, VARCHAR, BOOLEAN) IS 'Guest user creation with hardcoded email configuration';
COMMENT ON FUNCTION public.diagnose_email_system_v2() IS 'Diagnostic function that works without database parameters';

-- Step 6: Test the new functions
SELECT 'Testing new diagnostic function...' as test_step;
SELECT * FROM public.diagnose_email_system_v2();

-- Uncomment to test email function (replace with your email)
-- SELECT 'Testing new email function...' as test_step;
-- SELECT * FROM public.send_guest_verification_email_v3('<EMAIL>', '123456', gen_random_uuid());

-- Uncomment to test guest creation (replace with your email)
-- SELECT 'Testing new guest creation function...' as test_step;
-- SELECT * FROM public.create_guest_user_v3('<EMAIL>', 'Test User', 'Email Test', false);
