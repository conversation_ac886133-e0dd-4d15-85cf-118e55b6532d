import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON><PERSON>,
  <PERSON>,
  Button,
  Chip,
  Typography,
  useTheme
} from '@mui/material';
import {
  PersonOutline as GuestIcon,
  Login as LoginIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

interface GuestStatusBannerProps {
  variant?: 'banner' | 'compact';
  showUpgradeButton?: boolean;
}

const GuestStatusBanner: React.FC<GuestStatusBannerProps> = ({
  variant = 'banner',
  showUpgradeButton = true
}) => {
  const theme = useTheme();
  const { isGuest, logoutGuest } = useAuth();
  const navigate = useNavigate();

  if (!isGuest) {
    return null;
  }

  const handleSignIn = () => {
    if (logoutGuest) {
      logoutGuest();
    }
    navigate('/auth/login');
  };

  const handleUpgrade = () => {
    navigate('/auth/register');
  };

  if (variant === 'compact') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>        <Chip
          icon={<GuestIcon />}
          label="Guest Access"
          color="info"
          variant="outlined"
          size="small"
        />
        <Typography variant="caption" color="text.secondary">
          Limited features • Sign in for full access
        </Typography>
        <Button
          size="small"
          variant="text"
          onClick={handleSignIn}
          sx={{ ml: 'auto' }}
        >
          Sign In
        </Button>
      </Box>
    );
  }

  return (
    <Alert
      severity="info"
      sx={{
        mb: 3,
        borderRadius: 2,
        '& .MuiAlert-message': {
          width: '100%'
        }
      }}
      action={
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>          {showUpgradeButton && (
            <Button
              color="primary"
              variant="contained"
              size="small"
              startIcon={<StarIcon />}
              onClick={handleUpgrade}
              sx={{ minWidth: 'auto' }}
            >
              Sign Up
            </Button>
          )}
          <Button
            color="info"
            variant="outlined"
            size="small"
            startIcon={<LoginIcon />}
            onClick={handleSignIn}
            sx={{ minWidth: 'auto' }}
          >
            Sign In
          </Button>
        </Box>
      }
    >
      <AlertTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>        <GuestIcon fontSize="small" />
        Guest Access Active
      </AlertTitle>
      <Typography variant="body2">
        You're using DataStatPro as a guest. Some features are limited.
        <strong> Sign in or create an account</strong> to unlock full functionality,
        save your work, and access advanced features.
      </Typography>
      <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>        <Typography variant="caption" color="text.secondary">
          ✓ Basic statistical analysis
        </Typography>
        <Typography variant="caption" color="text.secondary">
          ✓ Data visualization
        </Typography>
        <Typography variant="caption" color="text.secondary">
          ✓ Import/export data
        </Typography>
      </Box>
    </Alert>
  );
};

export default GuestStatusBanner;