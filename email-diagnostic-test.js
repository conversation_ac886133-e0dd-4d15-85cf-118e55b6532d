/**
 * Email System Diagnostic Test
 * Run this in the browser console to diagnose email system issues
 */

// Configuration - Update these with your actual values
const SUPABASE_URL = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1MzM5MDUsImV4cCI6MjA2MjEwOTkwNX0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

// Test email - replace with your email
const TEST_EMAIL = '<EMAIL>';

/**
 * Test 1: Database Diagnostics
 */
async function testDatabaseDiagnostics() {
    console.log('🔍 Testing Database Diagnostics...');
    
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/diagnose_email_system`, {
            method: 'POST',
            headers: {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            console.error('❌ Database diagnostics failed:', response.status, response.statusText);
            return false;
        }
        
        const data = await response.json();
        
        console.log('📊 Diagnostic Results:');
        console.table(data);
        
        // Check if all components are properly configured
        const issues = data.filter(item => 
            item.status !== 'ENABLED' && 
            item.status !== 'CONFIGURED' && 
            item.status !== 'REACHABLE'
        );
        
        if (issues.length === 0) {
            console.log('✅ All database components are properly configured!');
            return true;
        } else {
            console.log('⚠️ Issues found:');
            issues.forEach(issue => {
                console.log(`- ${issue.component}: ${issue.status} - ${issue.action_required}`);
            });
            return false;
        }
        
    } catch (err) {
        console.error('❌ Diagnostic test failed:', err);
        return false;
    }
}

/**
 * Test 2: Guest User Creation
 */
async function testGuestUserCreation() {
    console.log('👤 Testing Guest User Creation...');
    
    try {
        const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/create_guest_user_v2`, {
            method: 'POST',
            headers: {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                p_email: TEST_EMAIL,
                p_full_name: 'Test User',
                p_institution: 'Email Test',
                p_marketing_consent: false
            })
        });
        
        if (!response.ok) {
            console.error('❌ Guest user creation failed:', response.status, response.statusText);
            const errorText = await response.text();
            console.error('Error details:', errorText);
            return false;
        }
        
        const data = await response.json();
        
        console.log('📧 Guest User Creation Results:');
        console.table(data);
        
        if (data && data.length > 0) {
            const result = data[0];
            if (result.success && result.email_sent) {
                console.log('✅ Guest user created and email sent successfully!');
                console.log(`📬 Check ${TEST_EMAIL} for the verification code: ${result.verification_token}`);
                return true;
            } else {
                console.log('⚠️ Guest user created but email failed:');
                console.log(`- Success: ${result.success}`);
                console.log(`- Email Sent: ${result.email_sent}`);
                console.log(`- Message: ${result.message}`);
                console.log(`- Email Error: ${result.email_error}`);
                return false;
            }
        } else {
            console.log('❌ No data returned from guest user creation');
            return false;
        }
        
    } catch (err) {
        console.error('❌ Guest user creation test failed:', err);
        return false;
    }
}

/**
 * Test 3: Edge Function Direct Test
 */
async function testEdgeFunctionDirect() {
    console.log('🔗 Testing Edge Function Direct...');
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/send-guest-verification-email`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: TEST_EMAIL,
                verificationCode: '123456',
                guestId: '00000000-0000-0000-0000-000000000000'
            })
        });
        
        const result = await response.text();
        
        console.log(`📡 Edge Function Response (${response.status}):`);
        console.log(result);
        
        if (response.status === 200) {
            console.log('✅ Edge Function is working correctly!');
            return true;
        } else if (response.status === 400) {
            console.log('⚠️ Edge Function reachable but returned validation error (expected for test data)');
            return true;
        } else {
            console.log('❌ Edge Function returned error status');
            return false;
        }
        
    } catch (err) {
        console.error('❌ Edge Function test failed:', err);
        return false;
    }
}

/**
 * Run All Tests
 */
async function runAllTests() {
    console.log('🚀 Starting Email System Diagnostic Tests...');
    console.log('=' .repeat(50));
    
    // Update test email if needed
    if (TEST_EMAIL === '<EMAIL>') {
        console.log('⚠️ Please update TEST_EMAIL in the script with your actual email address');
        return;
    }
    
    const results = {
        diagnostics: false,
        guestCreation: false,
        edgeFunction: false
    };
    
    // Test 1: Database Diagnostics
    results.diagnostics = await testDatabaseDiagnostics();
    console.log('');
    
    // Test 2: Guest User Creation
    results.guestCreation = await testGuestUserCreation();
    console.log('');
    
    // Test 3: Edge Function Direct Test
    results.edgeFunction = await testEdgeFunctionDirect();
    console.log('');
    
    // Summary
    console.log('📋 Test Summary:');
    console.log('=' .repeat(50));
    console.log(`Database Diagnostics: ${results.diagnostics ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Guest User Creation: ${results.guestCreation ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Edge Function Direct: ${results.edgeFunction ? '✅ PASS' : '❌ FAIL'}`);
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('🎉 All tests passed! Email system should be working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Please check the issues above and apply the necessary fixes.');
    }
    
    return results;
}

// Export functions for manual testing
window.emailDiagnostic = {
    runAllTests,
    testDatabaseDiagnostics,
    testGuestUserCreation,
    testEdgeFunctionDirect
};

console.log('🔧 Email Diagnostic Script Loaded!');
console.log('Run: emailDiagnostic.runAllTests() to start testing');
console.log('Or run individual tests like: emailDiagnostic.testDatabaseDiagnostics()');
