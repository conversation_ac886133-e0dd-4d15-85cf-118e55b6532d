# Email Guest System Fix Guide

## 🚨 Problem Analysis

The Email Guest verification system is failing to send emails despite showing success messages. Based on the codebase analysis, here are the potential issues and their solutions:

## 🔍 Root Cause Analysis

### Issue 1: Database Configuration Missing
The database functions require specific configuration parameters to call Edge Functions.

### Issue 2: HTTP Extension Not Enabled
PostgreSQL needs the `http` extension to make HTTP requests to Edge Functions.

### Issue 3: Function Parameter Mismatch
The AuthContext was missing the `p_marketing_consent` parameter (✅ **FIXED**).

### Issue 4: Error Handling Not Visible
Email sending errors are logged but not properly surfaced to the user interface.

## 🔧 Step-by-Step Fix

### Step 1: Apply Database Configuration

Run this SQL in your Supabase SQL Editor:

```sql
-- Enable HTTP extension
CREATE EXTENSION IF NOT EXISTS http;

-- Set database configuration
ALTER DATABASE postgres SET app.supabase_url = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
ALTER DATABASE postgres SET app.service_role_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjUzMzkwNSwiZXhwIjoyMDYyMTA5OTA1fQ.P4ro4H7Tn1Nx7NxeRZDV37ptWVk13aokfgggTcVSqOI';

-- Reload configuration
SELECT pg_reload_conf();
```

### Step 2: Verify Edge Function Environment Variables

In Supabase Dashboard → Edge Functions → Environment Variables, ensure:

- `RESEND_API_KEY`: Your Resend API key
- The Edge Function `send-guest-verification-email` is deployed

### Step 3: Test the System

Use the diagnostic tools I've created:

1. **Web Interface**: Visit `http://localhost:5174/email-debug.html`
2. **SQL Scripts**: Run `fix-email-system.sql` and `debug-email-flow.sql`

### Step 4: Run Diagnostics

```sql
-- Check system status
SELECT * FROM public.diagnose_email_system();

-- Debug email flow (replace with your email)
SELECT * FROM public.debug_email_sending('<EMAIL>');
```

## 🧪 Testing Procedure

### 1. Database Diagnostics
```sql
SELECT * FROM public.diagnose_email_system();
```
**Expected Result**: All components should show `ENABLED`, `CONFIGURED`, or `REACHABLE`.

### 2. Email Function Direct Test
```sql
SELECT * FROM public.send_guest_verification_email_v2(
    '<EMAIL>', 
    '123456', 
    gen_random_uuid()
);
```
**Expected Result**: `success = true`, `http_status = 200`.

### 3. Guest User Creation Test
```sql
SELECT * FROM public.create_guest_user_v2(
    '<EMAIL>',
    'Test User',
    'Email Test',
    false
);
```
**Expected Result**: `success = true`, `email_sent = true`.

### 4. Frontend Test
1. Navigate to `http://localhost:5174/auth`
2. Click "Email Guest"
3. Enter your email and click "Send Code"
4. Check browser console for detailed logs

## 🔍 Troubleshooting

### Issue: "HTTP extension not found"
**Solution**: Run `CREATE EXTENSION IF NOT EXISTS http;`

### Issue: "app.supabase_url not configured"
**Solution**: Run the database configuration SQL above.

### Issue: "RESEND_API_KEY not found"
**Solution**: Check Edge Function environment variables in Supabase Dashboard.

### Issue: "Edge Function unreachable"
**Solutions**:
1. Verify the function is deployed
2. Check function logs in Supabase Dashboard
3. Ensure service role key is correct

### Issue: "Domain not verified"
**Solution**: Ensure `datastatpro.live` is verified in your Resend account.

## 📊 Expected Flow

1. **Frontend**: User enters email → `createGuestUser()` called
2. **AuthContext**: Calls `create_guest_user_v2` with all parameters
3. **Database Function**: Creates user → calls `send_guest_verification_email_v2`
4. **Email Function**: Makes HTTP request to Edge Function
5. **Edge Function**: Sends email via Resend API
6. **Response**: Success/failure propagated back to frontend

## 🚀 Verification Steps

After applying fixes:

1. **Check Configuration**:
   ```sql
   SELECT name, setting FROM pg_settings 
   WHERE name IN ('app.supabase_url', 'app.service_role_key');
   ```

2. **Test Email System**:
   - Use `http://localhost:5174/email-debug.html`
   - Run all tests with your email address

3. **Monitor Logs**:
   - Browser console for frontend logs
   - Supabase Edge Function logs for backend issues

## 📝 Files Modified

- ✅ `src/context/AuthContext.tsx` - Added missing parameter and improved logging
- ✅ `src/types/index.ts` - Added `marketingConsent` to interface
- ✅ `supabase/functions/send-guest-verification-email/index.ts` - Updated domain
- 📄 Created diagnostic tools and SQL scripts

## 🎯 Next Steps

1. Run `fix-email-system.sql` in Supabase SQL Editor
2. Test using `http://localhost:5174/email-debug.html`
3. Try the Email Guest flow in the main application
4. Monitor browser console for detailed error messages

The system should now work correctly with proper error reporting!
