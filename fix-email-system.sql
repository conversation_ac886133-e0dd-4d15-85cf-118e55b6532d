-- Fix Email System Configuration
-- Run this in Supabase SQL Editor to fix email system issues

-- Step 1: Enable HTTP extension (required for calling Edge Functions)
CREATE EXTENSION IF NOT EXISTS http;

-- Step 2: Set database configuration parameters
-- Replace with your actual values if different
ALTER DATABASE postgres SET app.supabase_url = 'https://ghzibvkqmdlpyaidfbah.supabase.co';
ALTER DATABASE postgres SET app.service_role_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoemlidmtxbWRscHlhaWRmYmFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjUzMzkwNSwiZXhwIjoyMDYyMTA5OTA1fQ.P4ro4H7Tn1Nx7NxeRZDV37ptWVk13aokfgggTcVSqOI';

-- Step 3: Reload configuration
SELECT pg_reload_conf();

-- Step 4: Test the configuration
DO $$
DECLARE
    v_supabase_url TEXT;
    v_service_role_key TEXT;
    v_http_extension_exists BOOLEAN;
BEGIN
    -- Test if HTTP extension is enabled
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'http'
    ) INTO v_http_extension_exists;
    
    IF v_http_extension_exists THEN
        RAISE NOTICE '✅ HTTP extension is enabled';
    ELSE
        RAISE NOTICE '❌ HTTP extension is NOT enabled';
    END IF;
    
    -- Test if configuration is accessible
    BEGIN
        v_supabase_url := current_setting('app.supabase_url');
        v_service_role_key := current_setting('app.service_role_key');
        
        RAISE NOTICE '✅ Configuration applied successfully';
        RAISE NOTICE 'Supabase URL: %', v_supabase_url;
        RAISE NOTICE 'Service Role Key length: % characters', length(v_service_role_key);
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ Failed to read configuration: %', SQLERRM;
    END;
END
$$;

-- Step 5: Test the diagnostic function
SELECT 'Running diagnostic function...' as status;
SELECT * FROM public.diagnose_email_system();

-- Step 6: Test the email function directly (replace with your test email)
-- Uncomment and replace '<EMAIL>' with your actual email
/*
SELECT 'Testing email function directly...' as status;
SELECT * FROM public.send_guest_verification_email_v2(
    '<EMAIL>', 
    '123456', 
    gen_random_uuid()
);
*/

-- Step 7: Test guest user creation (replace with your test email)
-- Uncomment and replace '<EMAIL>' with your actual email
/*
-- Clean up any existing test user first
DELETE FROM public.guest_users WHERE email = '<EMAIL>';

SELECT 'Testing guest user creation...' as status;
SELECT * FROM public.create_guest_user_v2(
    '<EMAIL>',
    'Test User',
    'Email Debug Test',
    false
);
*/

-- Step 8: Check database settings
SELECT 'Checking database settings...' as status;
SELECT name, setting FROM pg_settings 
WHERE name IN ('app.supabase_url', 'app.service_role_key');

-- Step 9: Check HTTP extension
SELECT 'Checking HTTP extension...' as status;
SELECT extname, extversion FROM pg_extension WHERE extname = 'http';

-- Step 10: Show recent guest users (for debugging)
SELECT 'Recent guest users...' as status;
SELECT id, email, email_verified, created_at, updated_at 
FROM public.guest_users 
ORDER BY created_at DESC 
LIMIT 5;
