# Resend.com API Key Setup Guide

## Quick Setup (No Domain Verification Required)

You only need the **API Key** from Resend.com to send emails. No domain verification is required for basic email sending.

### Step 1: Create Resend Account
1. Go to [resend.com](https://resend.com)
2. Sign up for a free account
3. Verify your email address

### Step 2: Get Your API Key
1. After logging in, go to **API Keys** in the dashboard
2. Click **Create API Key**
3. Give it a name (e.g., "DataStatPro Email")
4. Select permissions: **Sending access** (default)
5. Click **Add**
6. **Copy the API key immediately** (it won't be shown again)

### Step 3: Configure DataStatPro
1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your DataStatPro project
3. Go to **Settings** → **Environment Variables**
4. Add these variables:
   - `RESEND_API_KEY`: Your API key from step 2
   - `FROM_EMAIL`: `<EMAIL>` (pre-verified by <PERSON>sen<PERSON>)

### Step 4: Test Email System
1. Visit: `http://localhost:5173/email-test`
2. Enter your email address
3. Click "Send Test Email"
4. Check your inbox for the verification code

## Important Notes

### ✅ What You DON'T Need:
- Domain verification
- DNS configuration
- Custom domain setup
- Paid plan (free tier works fine)

### ⚠️ Free Tier Limits:
- 100 emails per day
- 3,000 emails per month
- From address must be `<EMAIL>` OR your verified email

### 🔧 Troubleshooting:

**403 Error (Domain not verified):**
- Use `<EMAIL>` as `FROM_EMAIL` temporarily
- Or verify your domain if you want to use custom email

**500 Error:**
- Check if `RESEND_API_KEY` is set correctly in Supabase
- Verify the Edge Function is deployed

**Email not received:**
- Check spam folder
- Verify email address is correct
- Try with `<EMAIL>` as sender

## Alternative: Use Verified Email

If you want to use your own email address (like `<EMAIL>`):

1. In Resend dashboard, go to **Domains**
2. Click **Add Domain**
3. Enter your domain (e.g., `datastatpro.com`)
4. Follow DNS verification steps
5. Once verified, you can use any email from that domain

**But this is optional!** The system works fine with `<EMAIL>`.

---

## Quick Start Summary

1. **Get API Key**: Sign up at resend.com → API Keys → Create
2. **Set Environment Variables**: Supabase Dashboard → Settings → Environment Variables
   - `RESEND_API_KEY`: `re_xxxxxxxxxx`
   - `FROM_EMAIL`: `<EMAIL>` (recommended - no domain verification needed)
3. **Test**: Visit `/email-test` and send a test email

That's it! No domain setup required. 🎉