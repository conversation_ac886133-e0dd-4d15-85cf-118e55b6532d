# Manual Edge Function Deployment Guide

## Issue
The email verification system is still showing a 403 error because the deployed Edge Function might be using an older version. The current code is correct (using `<EMAIL>`), but we need to redeploy it.

## Solution: Manual Deployment via Supabase Dashboard

Since the Supabase CLI is not easily installable on this system, follow these steps to manually update the Edge Function:

### Step 1: Access Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in to your account
3. Select your DataStatPro project

### Step 2: Navigate to Edge Functions
1. In the left sidebar, click on **"Edge Functions"**
2. You should see the `send-guest-verification-email` function listed

### Step 3: Update the Function
1. Click on the `send-guest-verification-email` function
2. Click **"Edit Function"** or **"Update"**
3. Replace the entire function code with the content from:
   `d:\scout\DataStatPro\supabase\functions\send-guest-verification-email\index.ts`

### Step 4: Key Points to Verify
Make sure the function code contains this line (around line 170):
```typescript
from: 'DataStatPro <<EMAIL>>',
```

**NOT:**
```typescript
from: 'DataStatPro <<EMAIL>>',
```

### Step 5: Deploy the Function
1. Click **"Deploy"** or **"Save"**
2. Wait for the deployment to complete
3. You should see a success message

### Step 6: Verify Environment Variables
1. In the Supabase Dashboard, go to **"Settings" > "Environment Variables"**
2. Verify these variables are set:
   - `RESEND_API_KEY`: Your Resend API key
   - `FROM_EMAIL`: `<EMAIL>` (if used in code)

### Step 7: Test the Function
1. Go to your application: http://localhost:5173/email-test
2. Try sending a verification email to any email address
3. The error should be resolved

## Alternative: Copy-Paste Method

If the dashboard editor is difficult to use:

1. Open the file: `d:\scout\DataStatPro\supabase\functions\send-guest-verification-email\index.ts`
2. Copy the entire content (Ctrl+A, Ctrl+C)
3. In Supabase Dashboard > Edge Functions > send-guest-verification-email
4. Select all existing code and paste the new code
5. Deploy

## Expected Result

After deployment, the email system should work with any email address, not just `<EMAIL>`, because it will use the pre-verified `<EMAIL>` address.

## Troubleshooting

If you still get the 403 error after deployment:
1. Check the function logs in Supabase Dashboard
2. Verify the `RESEND_API_KEY` is correctly set
3. Ensure the function code was properly updated
4. Try clearing browser cache and testing again